﻿<UserControl x:Class="BetterGenshinImpact.View.Pages.OneDragonFlowPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dd="urn:gong-wpf-dragdrop"
             xmlns:local="clr-namespace:BetterGenshinImpact.View.Pages"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:oneDragon="clr-namespace:BetterGenshinImpact.ViewModel.Pages.OneDragon"
             xmlns:oneDragonView="clr-namespace:BetterGenshinImpact.View.Pages.OneDragon"
             xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             d:DataContext="{d:DesignInstance Type=pages:OneDragonFlowViewModel}"
             d:DesignHeight="1000"
             d:DesignWidth="800"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             FontFamily="{StaticResource TextThemeFontFamily}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             mc:Ignorable="d">
    
    <b:Interaction.Triggers>
        <b:EventTrigger EventName="Loaded">
            <b:InvokeCommandAction Command="{Binding LoadedCommand}" CommandParameter="{Binding}" />
        </b:EventTrigger>
    </b:Interaction.Triggers>
    
    
    
    
    <Grid Margin="22,16,8,12">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" MaxWidth="300" />
            <ColumnDefinition Width="2*" />
        </Grid.ColumnDefinitions>

        <!--  左侧任务列表  -->
       <DockPanel Grid.Column="0">
           <DockPanel.ContextMenu>
               <ContextMenu>
                   <MenuItem Command="{Binding AddTaskGroupCommand}" Header="新增配置" />
                   <MenuItem Command="{Binding DeleteTaskGroupCommand}"
                             CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType=ui:ListView}}"
                             Header="删除配置" />
           </ContextMenu>
       </DockPanel.ContextMenu>
       <Grid DockPanel.Dock="Top"
        Margin="0,0,0,10">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>
        <TextBlock
            Grid.Column="0"
            Grid.Row="0"
            VerticalAlignment="Center"
            FontSize="16"
            FontWeight="Bold"
            Text="任务列表" />
        <ui:Button
            Grid.Column="1"
            Grid.Row="0"
            Margin="0,0,10,0"
            Appearance="Primary"
            Icon="{ui:SymbolIcon Play24}"
            Command="{Binding OneKeyExecuteCommand}" />
        <ui:Button
            Grid.Column="2"
            Grid.Row="0"
            Margin="0,0,10,0"
            Icon="{ui:SymbolIcon Add24}"
            Command="{Binding AddTaskGroupCommand}" />
    </Grid>
    <Grid DockPanel.Dock="Top"
        Margin="0,0,0,10">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Separator Grid.Row="0"  Margin="0,1.5,6,0" BorderThickness="0,1,0,0" />
    </Grid>
    <ContentControl>
        <ContentControl.Style>
            <Style TargetType="ContentControl">
                <Setter Property="Content">
                    <Setter.Value>
                        <ui:ListView dd:DragDrop.IsDragSource="True"
                                     dd:DragDrop.IsDropTarget="True"
                                     dd:DragDrop.UseDefaultDragAdorner="True"
                                     ItemsSource="{Binding TaskList}"
                                     SelectedItem="{Binding SelectedTask, Mode=TwoWay}"
                                     SelectionMode="Single">
                            <ui:ListView.ItemTemplate>
                                <DataTemplate>
                                    <ui:Card Margin="0,2,14,2">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <ui:FontIcon Grid.Column="0"
                                                         Glyph="&#xe411;"
                                                         Style="{StaticResource FaFontIconStyleForOneDragon}" />
                                            <Ellipse Grid.Column="1"
                                                     Width="10"
                                                     Height="10"
                                                     Margin="10,0,10,0"
                                                     Fill="{Binding StatusColor}"
                                                     IsHitTestVisible="False" />
                                            <TextBlock Grid.Column="2"
                                                       VerticalAlignment="Center"
                                                       IsHitTestVisible="False"
                                                       Text="{Binding Name}" Width="100"/>
                                            
                                            
                                            <ui:ToggleSwitch Grid.Column="3" IsChecked="{Binding IsEnabled, Mode=TwoWay}" />
                                        </Grid>
                                    </ui:Card>
                                </DataTemplate>
                            </ui:ListView.ItemTemplate>
                            <ui:ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                    <Setter Property="Background" Value="Transparent" />
                                    <Setter Property="BorderThickness" Value="0" />
                                    <Setter Property="Padding" Value="0" />
                                    <!--  添加以下设置来禁用选中效果  -->
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="{x:Type ListViewItem}">
                                                <Border x:Name="Bd"
                                                        Padding="{TemplateBinding Padding}"
                                                        Background="{TemplateBinding Background}"
                                                        SnapsToDevicePixels="true">
                                                    <ContentPresenter
                                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsEnabled" Value="false">
                                                        <Setter Property="Foreground"
                                                                Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </ui:ListView.ItemContainerStyle>
                        </ui:ListView>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding TaskList.Count}" Value="0">
                        <Setter Property="ContentTemplate">
                            <Setter.Value>
                                <DataTemplate>
                                    <TextBlock Margin="0,0,0,0"
                                               Text="      ± 鼠标右击添加或删除任务       "
                                               VerticalAlignment="Top"
                                               HorizontalAlignment="Left"
                                               FontSize="14"
                                               Height="800"
                                               Width="200"
                                               Foreground="{DynamicResource TextFillColorTertiaryBrush}" />
                                </DataTemplate>
                            </Setter.Value>
                        </Setter>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ContentControl.Style>
    </ContentControl>
</DockPanel>
        <!--  右侧配置  -->
        <!--<ContentControl Grid.Column="1"
                        Margin="20,0,0,0"
                        Content="{Binding SelectedTask.ViewModel}">
            <ContentControl.Resources>
                <DataTemplate DataType="{x:Type oneDragon:MailViewModel}">
                    <oneDragonView:MailPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:CraftViewModel}">
                    <oneDragonView:CraftPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:DailyCommissionViewModel}">
                    <oneDragonView:DailyCommissionPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:DailyRewardViewModel}">
                    <oneDragonView:DailyRewardPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:DomainViewModel}">
                    <oneDragonView:DomainPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:ForgingViewModel}">
                    <oneDragonView:ForgingPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:LeyLineBlossomViewModel}">
                    <oneDragonView:LeyLineBlossomPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:SereniteaPotViewModel}">
                    <oneDragonView:SereniteaPotPage />
                </DataTemplate>
                <DataTemplate DataType="{x:Type oneDragon:TcgViewModel}">
                    <oneDragonView:TcgPage />
                </DataTemplate>
            </ContentControl.Resources>
        </ContentControl>-->

        <!--  右侧配置  -->
        <DockPanel Grid.Column="1" Margin="16,0,0,0">
            <StackPanel Margin="0,0,0,10" DockPanel.Dock="Top">
                <Grid Margin="0,0,9,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0"
                               FontSize="16"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               Text="配置" />
                    <ComboBox Grid.Column="1"
                              MinWidth="200"
                              Height="34"
                              Margin="10,0,10,0"
                              DisplayMemberPath="Name"
                              ItemsSource="{Binding ConfigList}"
                              SelectedItem="{Binding SelectedConfig, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="SelectionChanged">
                                <b:InvokeCommandAction Command="{Binding ConfigDropDownChangedCommand}"
                                                       CommandParameter="GeniusInvocation" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ComboBox>
                    <ui:Button
                        Grid.Column="2"
                        Margin="0,0,10,0"
                        Icon="{ui:SymbolIcon Add24}"
                        Command="{Binding AddConfigCommand}" />
                    <!--<ui:Button Grid.Column="3"
                               Icon="{ui:SymbolIcon Save24}"
                               Command="{Binding SaveConfigurationCommand}"
                               Content="保存配置" />-->
                </Grid>
                <Separator Margin="0,5,0,0" BorderThickness="0,1,0,0" />
            </StackPanel>

            <ScrollViewer>
                <StackPanel Margin="0,0,18,0">
                    <!--  合成树脂  -->
                    <TextBlock Margin="4,0,0,4"
                               FontSize="14"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               Text="合成树脂" />
                    
                    <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon WeatherMoon24}">

                        <ui:CardControl.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              Width="Auto"
                                              HorizontalAlignment="Left"
                                              FontTypography="Body"
                                              Text="合成树脂合成台"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Margin=" 0,0,0,0"
                                              Width="Auto"
                                              HorizontalAlignment="Left"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="指定地区合成树脂"
                                              TextWrapping="Wrap" />
                                <ComboBox 
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Grid.RowSpan="2" 
                                          Margin="18,0,0,0"
                                          Width="Auto"
                                          HorizontalContentAlignment="Center"
                                          HorizontalAlignment="Left"
                                          SelectedItem="{Binding SelectedConfig.CraftingBenchCountry, Mode=TwoWay}"
                                          ItemsSource="{Binding CraftingBenchCountry}" />
                                
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="2"
                                              Width="Auto"
                                              Margin=" 15,0,0,0"
                                              HorizontalAlignment="Left"
                                              FontTypography="Body"
                                              Text="合成后保留"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="2"
                                              Margin=" 15,0,0,0"
                                              Width="Auto"
                                              HorizontalAlignment="Left"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="原粹树脂数量"
                                              TextWrapping="Wrap" />
                                <TextBox Grid.Row="0"
                                         Grid.Column="3"
                                         Grid.RowSpan="2" 
                                         Margin="8,0,0,0" 
                                         Width="Auto"
                                         HorizontalContentAlignment="Center"
                                         Text="{Binding SelectedConfig.MinResinToKeep, Mode=TwoWay, TargetNullValue=-1}"
                                         Height="{Binding ElementName=combinedTextBlocks, Path=ActualHeight}"/>

                                
                            </Grid>
                        </ui:CardControl.Header>
                        
                    </ui:CardControl>


                    <!--  自动秘境  -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Bottom" Margin="4,0,0,4">
                        <TextBlock FontSize="14"
                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                   Text="自动秘境" />
                        <TextBlock Margin="4,0,0,0"
                                   VerticalAlignment="Center"
                                   FontSize="11"
                                   Text="（此处未覆盖的配置可在 独立任务-自动秘境 中配置）" />
                    </StackPanel>
 

                    <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
                        <ui:CardExpander.Icon>
                            <ui:FontIcon Glyph="&#xf073;" Style="{StaticResource FaFontIconStyle}" />
                        </ui:CardExpander.Icon>
                        <ui:CardExpander.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="每日秘境刷取配置"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="前往指定秘境消耗树脂，并自动领取奖励。"
                                              TextWrapping="Wrap">
                                </ui:TextBlock>
                                <ui:ToggleSwitch Grid.Row="0"
                                                 Grid.RowSpan="2"
                                                 Grid.Column="1"
                                                 Margin="0,0,20,0"
                                                 IsChecked="{Binding SelectedConfig.WeeklyDomainEnabled, Converter={StaticResource InverseBooleanConverter}, Mode=TwoWay}" />
                            </Grid>
                        </ui:CardExpander.Header>
                        <StackPanel>
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="进入秘境切换的队伍名称"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="注意是游戏内你设置的名称"
                                              TextWrapping="Wrap" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="1"
                                            MinWidth="150"
                                            MaxWidth="800"
                                            Margin="20,0,36,0"
                                            Text="{Binding SelectedConfig.PartyName, Mode=TwoWay}"
                                            PlaceholderText="填写队伍名称"
                                            TextAlignment="Center"
                                            TextWrapping="Wrap" />
                            </Grid>

                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              HorizontalAlignment="Left"
                                              Text="选择秘境"
                                              Width="Auto"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              HorizontalAlignment="Left"
                                              Text="秘境名称"
                                              Width="Auto"
                                              TextWrapping="Wrap" />
                                <ComboBox Grid.Row="0"
                                          Grid.RowSpan="2"
                                          Grid.Column="1"
                                          Width="Auto"
                                          HorizontalAlignment="Left"
                                          HorizontalContentAlignment="Center"
                                          Margin="10,0,10,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.DomainName, Mode=TwoWay}">
                                </ComboBox>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="2"
                                              Margin="10,0,10,0"
                                              FontTypography="Body"
                                              HorizontalAlignment="Right"
                                              Text="周日或限时"
                                              Width="Auto"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="2"
                                              Margin="10,0,10,0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              HorizontalAlignment="Right"
                                              Text="选择序号"
                                              Width="Auto"
                                              TextWrapping="Wrap" />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="3"
                                          Grid.RowSpan="2"
                                          MinWidth="60"
                                          Margin="0,0,36,0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          HorizontalContentAlignment="Center"
                                          HorizontalAlignment="Stretch" 
                                          ItemsSource="{Binding SundayEverySelectedValueList}"
                                          SelectedItem="{Binding SelectedConfig.SundayEverySelectedValue, Mode=TwoWay}">
                                </ComboBox>

                            </Grid>
                        </StackPanel>
                    </ui:CardExpander>

                    <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
                        <ui:CardExpander.Icon>
                            <ui:FontIcon Glyph="&#xf784;" Style="{StaticResource FaFontIconStyle}" />
                        </ui:CardExpander.Icon>
                        <ui:CardExpander.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="每周秘境刷取配置"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="启用后，每日刷取配置将会失效。"
                                              TextWrapping="Wrap" />
                                <ui:ToggleSwitch Grid.Row="0"
                                                 Grid.RowSpan="2"
                                                 Grid.Column="1"
                                                 Margin="0,0,20,0"
                                                 IsChecked="{Binding SelectedConfig.WeeklyDomainEnabled, Mode=TwoWay}" />
                            </Grid>
                        </ui:CardExpander.Header>
                        <StackPanel>
                            
                                
                     
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                                Margin="0,20,0,0"
                                              HorizontalAlignment="Center"
                                              FontTypography="Body"
                                              FontSize="12"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="周期始于凌晨 4:00 ，如周一 4:00 至周二 3:59 刷取周一秘境" 
                                              TextWrapping="Wrap">
                                </ui:TextBlock>
                           
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              Margin="38,0,0,0"
                                              HorizontalAlignment="Center"
                                              Text="周一"
                                              VerticalAlignment="Center" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.Column="1"
                                            MinWidth="140"
                                            Margin="5,0,12,0"
                                            Text="{Binding SelectedConfig.MondayPartyName, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center"
                                            PlaceholderText="填写队伍名称 " />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="2"
                                          Width="140"
                                          Margin="0,0,36,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.MondayDomainName, Mode=TwoWay}" />
                            </Grid>
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Margin="38,0,0,0"
                                              HorizontalAlignment="Center"
                                              Text="周二"
                                              VerticalAlignment="Center" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.Column="1"
                                            MinWidth="140"
                                            Margin="5,0,12,0"
                                            Text="{Binding SelectedConfig.TuesdayPartyName, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center"
                                            PlaceholderText="填写队伍名称 " />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="2"
                                          Width="140"
                                          Margin="0,0,36,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.TuesdayDomainName, Mode=TwoWay}" />
                            </Grid>
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              Margin="38,0,0,0"
                                              HorizontalAlignment="Center"
                                              Text="周三"
                                              VerticalAlignment="Center" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.Column="1"
                                            MinWidth="140"
                                            Margin="5,0,12,0"
                                            Text="{Binding SelectedConfig.WednesdayPartyName, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center"
                                            PlaceholderText="填写队伍名称 " />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="2"
                                          Width="140"
                                          Margin="0,0,36,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.WednesdayDomainName, Mode=TwoWay}" />

                            </Grid>
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              Margin="38,0,0,0"
                                              HorizontalAlignment="Center"
                                              Text="周四"
                                              VerticalAlignment="Center" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.Column="1"
                                            MinWidth="140"
                                            Margin="5,0,12,0"
                                            Text="{Binding SelectedConfig.ThursdayPartyName, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center"
                                            PlaceholderText="填写队伍名称 " />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="2"
                                          Width="140"
                                          Margin="0,0,36,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.ThursdayDomainName, Mode=TwoWay}" />

                            </Grid>
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              Margin="38,0,0,0"
                                              HorizontalAlignment="Center"
                                              Text="周五"
                                              VerticalAlignment="Center" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.Column="1"
                                            MinWidth="140"
                                            Margin="5,0,12,0"
                                            Text="{Binding SelectedConfig.FridayPartyName, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center"
                                            PlaceholderText="填写队伍名称 " />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="2"
                                          Width="140"
                                          Margin="0,0,36,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.FridayDomainName, Mode=TwoWay}" />


                            </Grid>
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              Margin="38,0,0,0"
                                              HorizontalAlignment="Center"
                                              Text="周六"
                                              VerticalAlignment="Center" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.Column="1"
                                            MinWidth="140"
                                            Margin="5,0,12,0"
                                            Text="{Binding SelectedConfig.SaturdayPartyName, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center"
                                            PlaceholderText="填写队伍名称 " />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="2"
                                          Width="140"
                                          Margin="0,0,36,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.SaturdayDomainName, Mode=TwoWay}" />

                            </Grid>
                            <Grid Margin="16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              Margin="38,0,0,0"
                                              HorizontalAlignment="Center"
                                              Text="周日"
                                              VerticalAlignment="Center" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.Column="1"
                                            MinWidth="140"
                                            Margin="5,0,12,0"
                                            Text="{Binding SelectedConfig.SundayPartyName, Mode=TwoWay}"
                                            TextWrapping="Wrap"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center"
                                            PlaceholderText="填写队伍名称 " />
                                <ComboBox Grid.Row="0"
                                          Grid.Column="2"
                                          Width="140"
                                          Margin="0,0,36,0"
                                          ItemsSource="{Binding DomainNameList}"
                                          SelectedItem="{Binding SelectedConfig.SundayDomainName, Mode=TwoWay}" />
                                <Border Grid.Row="1"
                                        Grid.Column="0"
                                        Grid.ColumnSpan="3"
                                        Grid.RowSpan="2"
                                        Margin="0,21,100,10"
                                        Background="#80595959"
                                        CornerRadius="5"
                                        Width="Auto"
                                        HorizontalAlignment="Right"
                                        Padding="10">
                                    <ui:TextBlock FontTypography="Body"
                                                  FontSize="13" 
                                                  Text="周日或限时三种奖励,从上往下(1/2/3)序号:"
                                                  TextAlignment="Justify"
                                                  TextWrapping="Wrap" />
                                </Border>
                                <ComboBox Grid.Row="1"
                                          Grid.Column="2"
                                          Grid.RowSpan="2"
                                          Margin="20,11,37,0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          HorizontalAlignment="Right"
                                          VerticalAlignment="Center"
                                          ItemsSource="{Binding SundaySelectedValueList , Mode=TwoWay}"
                                          SelectedItem="{Binding SelectedConfig.SundaySelectedValue, Mode=TwoWay}">
                                </ComboBox>
                            </Grid>
                        </StackPanel>
                    </ui:CardExpander>

                    <ui:CardControl Margin="0,0,0,12">
                        <ui:CardControl.Icon>
                            <ui:FontIcon Glyph="&#xf68a;" Style="{StaticResource FaFontIconStyle}" />
                        </ui:CardControl.Icon>
                        <ui:CardControl.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                
                                <!-- 使用 Button 控制弹窗 -->
                                <ToggleButton Grid.Row="0"
                                              Grid.Column="0"   
                                              Grid.RowSpan="2"
                                              Height="36"
                                              VerticalAlignment="Center"
                                              HorizontalAlignment="Center"
                                              Margin="0,0,0,0"
                                              Content="领奖树脂设定"
                                              x:Name="ToggleButton">
                                </ToggleButton>
                                
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="1"
                                              FontTypography="Body"
                                              HorizontalAlignment="Right"
                                              Text="分解圣遗物"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="1"
                                              HorizontalAlignment="Right"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="最高的星级"
                                              TextWrapping="Wrap" />
                                
                                <!-- 弹窗 -->
                                <Popup Grid.Row="0" Grid.Column="0" x:Name="ResinPopup"
                                       Placement="Center"
                                       PlacementTarget="{Binding ElementName=ToggleButton}"
                                       IsOpen="{Binding IsChecked, ElementName=ToggleButton}"
                                       AllowsTransparency="True"
                                       PopupAnimation="Fade"
                                       HorizontalAlignment="Center"
                                       Focusable="False" 
                                       StaysOpen="False">
                                    <Border CornerRadius="8"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Top"
                                            Background="{DynamicResource  ApplicationBackgroundBrush}"
                                            BorderBrush="{DynamicResource SystemAccentColorPrimaryBrush}"
                                            BorderThickness="1"
                                            Margin="0,0,0,0">
                                    
                                        <StackPanel Margin="12">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="*" />
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>
                                                <!-- 模式选择 -->
                                                <ToggleButton Grid.Column="0"
                                                        Margin="0,0,0,0"
                                                        IsChecked="{Binding Config.AutoDomainConfig.SpecifyResinUse, Mode=TwoWay}"
                                                        Content="模式切换" />
                                                
                                                <!-- 根据模SpecifyResinUse显示不同的文字 -->
                                                <ui:TextBlock Grid.Column="1"
                                                                Margin="12,0,0,0"
                                                                FontSize="14"
                                                                VerticalAlignment="Center"
                                                                HorizontalAlignment="Right"
                                                                Text="先用浓缩,后原粹,其余不用"
                                                                Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                TextWrapping="Wrap"
                                                                Visibility="{Binding Config.AutoDomainConfig.SpecifyResinUse, Converter={StaticResource BooleanToVisibilityRevertConverter}}" />
                                                <ui:TextBlock Grid.Column="1"
                                                                Margin="5,0,0,0"
                                                                FontSize="14"
                                                                VerticalAlignment="Center"
                                                                HorizontalAlignment="Right"
                                                                Text="按下方配置数量使用树脂"
                                                                Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                TextWrapping="Wrap"
                                                                Visibility="{Binding Config.AutoDomainConfig.SpecifyResinUse, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                            </Grid>
                                            
                                            <StackPanel Margin="0,10,0,0" IsEnabled="{Binding Config.AutoDomainConfig.SpecifyResinUse}" Visibility="{Binding Config.AutoDomainConfig.SpecifyResinUse, Converter={StaticResource BooleanToVisibilityConverter}}">   
                                                <!-- 细线 -->
                                                <Separator Height="1"
                                                           Margin="0,0,0,0"
                                                           Background="Gray"/>
                                                
                                                <!-- 原粹树脂设置 -->
                                                <StackPanel Orientation="Horizontal"
                                                            Margin="0,10,0,12">
                                                    <ui:TextBlock Text="原粹树脂刷取次数："
                                                                  VerticalAlignment="Center"
                                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                  Margin="0,0,8,0" />
                                                    <ui:NumberBox Value="{Binding Config.AutoDomainConfig.OriginalResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                                  Minimum="0"
                                                                  SmallChange="1"
                                                                  LargeChange="5"
                                                                  SpinButtonPlacementMode="Inline"
                                                                  Width="120"/>
                                                </StackPanel>
                                                
                                                <!-- 浓缩树脂设置 -->
                                                <StackPanel Orientation="Horizontal"
                                                            Margin="0,0,0,12">
                                                    <ui:TextBlock Text="浓缩树脂刷取次数："
                                                                  VerticalAlignment="Center"
                                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                  Margin="0,0,8,0" />
                                                    <ui:NumberBox Value="{Binding Config.AutoDomainConfig.CondensedResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                                  Minimum="0"
                                                                  SmallChange="1"
                                                                  LargeChange="5"
                                                                  SpinButtonPlacementMode="Inline"
                                                                  Width="120" />
                                                </StackPanel>

                                                <!-- 须臾树脂设置 -->
                                                <StackPanel Orientation="Horizontal"
                                                            Margin="0,0,0,12">
                                                    <ui:TextBlock Text="须臾树脂刷取次数："
                                                                  VerticalAlignment="Center"
                                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                  Margin="0,0,8,0" />
                                                    <ui:NumberBox Value="{Binding Config.AutoDomainConfig.TransientResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                                  Minimum="0"
                                                                  SmallChange="1"
                                                                  LargeChange="5"
                                                                  SpinButtonPlacementMode="Inline"
                                                                  Width="120" />
                                                </StackPanel>

                                                <!-- 脆弱树脂设置 -->
                                                <StackPanel Orientation="Horizontal"
                                                            Margin="0,0,0,0">
                                                    <ui:TextBlock Text="脆弱树脂刷取次数："
                                                                  VerticalAlignment="Center"
                                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                  Margin="0,0,8,0" />
                                                    <ui:NumberBox Value="{Binding Config.AutoDomainConfig.FragileResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                                  Minimum="0"
                                                                  SmallChange="1"
                                                                  LargeChange="5"
                                                                  SpinButtonPlacementMode="Inline"
                                                                  Width="120" />
                                                </StackPanel>
                                            </StackPanel>
                                        </StackPanel>
                                    </Border>
                                </Popup>
                                
                            </Grid>
                        </ui:CardControl.Header>
                        <StackPanel Orientation="Horizontal">
                            <ComboBox
                                Width="60"
                                Margin="0,0,10,0"
                                ItemsSource="{Binding  Source={x:Static pages:TaskSettingsPageViewModel.ArtifactSalvageStarList}}"
                                SelectedItem="{Binding Config.AutoArtifactSalvageConfig.MaxArtifactStar, Mode=TwoWay}">
                            </ComboBox>
                            <ui:ToggleSwitch
                                Margin="0,0,36,0"
                                IsChecked="{Binding Config.AutoDomainConfig.AutoArtifactSalvage, Mode=TwoWay}" />
                        </StackPanel>
                    </ui:CardControl>

                    <!--  领取奖励  -->
                    <TextBlock Margin="4,0,0,4"
                               FontSize="14"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               Text="领取奖励" />
                    <ui:CardControl Margin="0,0,0,12">
                        <ui:CardControl.Icon>
                            <ui:FontIcon Glyph="&#xf14e;" Style="{StaticResource FaFontIconStyle}" />
                        </ui:CardControl.Icon>
                        <ui:CardControl.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="领取奖励的冒险者协会"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="前往指定地区冒险者协会领取"
                                              TextWrapping="Wrap" />
                            </Grid>
                        </ui:CardControl.Header>
                        <ComboBox MinWidth="100"
                                  Margin="0,0,28,0"
                                  SelectedItem="{Binding SelectedConfig.AdventurersGuildCountry, Mode=TwoWay}"
                                  ItemsSource="{Binding AdventurersGuildCountry}" />
                    </ui:CardControl>
                    <ui:CardControl Margin="0,0,0,12">
                        <ui:CardControl.Icon>
                            <ui:FontIcon Glyph="&#xf4c7;" Style="{StaticResource FaFontIconStyle}" />
                        </ui:CardControl.Icon>
                        <ui:CardControl.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="领取前切换队伍（好感队）"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              TextAlignment="Center"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="用于给指定队伍加好感度"
                                              TextWrapping="Wrap" />
                            </Grid>
                        </ui:CardControl.Header>
                        <ui:TextBox
                            MinWidth="150"
                            MaxWidth="800"
                            Margin="0,0,28,0"
                            Text="{Binding SelectedConfig.DailyRewardPartyName, Mode=TwoWay}"
                            PlaceholderText="填写好感队名称"
                            TextAlignment="Center"
                            VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            TextWrapping="Wrap" />
                    </ui:CardControl>

                    <!--  尘歌壶  -->
                    <TextBlock Margin="4,0,0,4"
                               FontSize="14"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               Text="尘歌壶配置" />
                   
                    <ui:CardControl Margin="0,0,0,12">
                        <ui:CardControl.Icon>
                            <ui:FontIcon Glyph="&#xf07a;" Style="{StaticResource FaFontIconStyle}" />
                        </ui:CardControl.Icon>
                        <ui:CardControl.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="进壶方式选择"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="购买日期和商品"
                                              TextWrapping="Wrap" />
                            </Grid>
                        </ui:CardControl.Header>
                        <StackPanel Orientation="Horizontal">
                            <ComboBox MinWidth="90"
                                      Margin="0,0,10,0"
                                      SelectedItem="{Binding SelectedConfig.SereniteaPotTpType, Mode=TwoWay}"
                                      ItemsSource="{Binding SereniteaPotTpTypes}" />
                            <Button MinWidth="90"
                                    Height="36"
                                    Margin="0,0,28,0"
                                    Content="购买选择"
                                    Command="{Binding AddPotBuyItemCommand}" />
                        </StackPanel>
                    </ui:CardControl>
                    
                    <!--  完成后操作  -->
                    <TextBlock Margin="4,0,0,4"
                               FontSize="14"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               Text="完成后操作" />
                    <ui:CardControl Margin="0,0,0,12">
                        <ui:CardControl.Icon>
                            <ui:FontIcon Glyph="&#xf11e;" Style="{StaticResource FaFontIconStyle}" />
                        </ui:CardControl.Icon>
                        <ui:CardControl.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="任务完成后执行的操作"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="一条龙结束后操作"
                                              TextWrapping="Wrap" />
                            </Grid>
                        </ui:CardControl.Header>
                        <ComboBox MinWidth="150"
                                  Margin="0,0,28,0"
                                  SelectedItem="{Binding SelectedConfig.CompletionAction, Mode=TwoWay}"
                                  ItemsSource="{Binding CompletionActionList}" />
                    </ui:CardControl>

                    <!--  可以继续添加其他设置项  -->

                </StackPanel>
            </ScrollViewer>
        </DockPanel>
    </Grid>
</UserControl>
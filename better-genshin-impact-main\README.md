<div align="center">
  <h1 align="center">
    <a href="https://bettergi.com/"><img src="https://img.alicdn.com/imgextra/i2/2042484851/O1CN014wn1rf1lhoFYjL0gA_!!2042484851.png" width="200"></a>
    <br/>
    <a href="https://bettergi.com/">BetterGI</a>
  </h1>
  <a href="https://trendshift.io/repositories/5269" target="_blank"><img src="https://trendshift.io/api/badge/repositories/5269" alt="babalae%2Fbetter-genshin-impact | Trendshift" style="width: 200px; height: 46px;" width="250" height="46"/></a>
</div>

<br/>

<div align="center">
  <a href="https://dotnet.microsoft.com/zh-cn/download/dotnet/latest/runtime"><img alt="Windows" src="https://img.shields.io/badge/platform-Windows-blue?logo=windowsxp&style=flat-square&color=1E9BFA" /></a>
  <a href="https://github.com/babalae/better-genshin-impact/releases"><img alt="下载数" src="https://img.shields.io/github/downloads/babalae/better-genshin-impact/total?logo=github&style=flat-square&color=1E9BFA"></a>
  <a href="https://github.com/babalae/better-genshin-impact/releases"><img alt="Release" src="https://img.shields.io/github/v/release/babalae/better-genshin-impact?logo=visualstudio&style=flat-square&color=1E9BFA"></a>
</div>

<br/>


<div align="center">
🌟 点一下右上角的 Star，Github 主页就能收到软件更新通知了哦~
</div>

<div align="center">
    <img src="https://img.alicdn.com/imgextra/i1/2042484851/O1CN01OL1E1v1lhoM7Wdmup_!!2042484851.gif" alt="Star" width="186" height="60">
  </a>
</div>

<br/>

[English](./Docs/readme_en.md) | [中文](./README.md)| [繁体中文](./Docs/readme_tcn.md)  
BetterGI · 更好的原神， 一个基于计算机视觉技术，意图让原神变的更好的项目。

## 功能
* 实时任务
  * [自动拾取](https://bettergi.com/feats/timer/pick.html)：遇到可交互/拾取内容时自动按 <kbd>F</kbd>，支持黑白名单配置
  * [自动剧情](https://bettergi.com/feats/timer/skip.html)：快速点击过剧情、自动选择选项、自动提交物品、关闭弹出书页等
    * 与凯瑟琳对话时有橙色选项会 [自动领取「每日委托」奖励](https://bettergi.com/feats/timer/skip.html#%E8%87%AA%E5%8A%A8%E9%A2%86%E5%8F%96%E3%80%8E%E6%AF%8F%E6%97%A5%E5%A7%94%E6%89%98%E3%80%8F%E5%A5%96%E5%8A%B1)、[自动重新派遣](https://bettergi.com/feats/timer/skip.html#%E8%87%AA%E5%8A%A8%E9%87%8D%E6%96%B0%E6%B4%BE%E9%81%A3)
  * [自动邀约](https://bettergi.com/feats/timer/skip.html#%E8%87%AA%E5%8A%A8%E9%82%80%E7%BA%A6)：自动剧情开启的情况下此功能才会生效，自动选择邀约选项
  * [快速传送](https://bettergi.com/feats/timer/tp.html)：在地图上点击传送点，或者点击后出现的列表中存在传送点，会自动点击传送点并传送
  * [半自动钓鱼](https://bettergi.com/feats/timer/fish.html)：AI 识别自动抛竿，鱼上钩时自动收杆，并自动完成钓鱼进度
  * [自动烹饪](https://bettergi.com/feats/timer/cook.html)：自动在完美区域完成食物烹饪，暂不支持“仙跳墙”
* 独立任务
  * [全自动七圣召唤](https://bettergi.com/feats/task/tcg.html)：帮助你轻松完成七圣召唤角色邀请、每周来客挑战等 PVE 内容
  * [自动伐木](https://bettergi.com/feats/task/felling.html)：自动 <kbd>Z</kbd> 键使用「王树瑞佑」，利用上下线可以刷新木材的原理，挂机刷满一背包的木材
  * [自动秘境](https://bettergi.com/feats/task/domain.html)：全自动秘境挂机刷体力，自动循环进入秘境开启钥匙、战斗、走到古树并领取奖励
  * [自动音游](https://bettergi.com/feats/task/music.html)：一键自动完成千音雅集的专辑，快速获取成就
  * [全自动钓鱼](https://bettergi.com/feats/task/fish.html)：在出现钓鱼F按钮的位置面向鱼塘，然后启动全自动钓鱼，启动后程序会自动完成钓鱼，并切换白天和晚上
* 全自动
  * [一条龙](https://github.com/babalae/better-genshin-impact/issues/846)：一键完成日常（使用历练点），并领取奖励
  * [自动采集/挖矿/锄地](https://bettergi.com/feats/autos/pathing.html)：通过左上角小地图的识别，完成自动采集、挖矿、锄地等功能
  * [键鼠录制](https://bettergi.com/feats/autos/kmscript.html)：可以录制回放当前的键鼠操作，建议配合调度器使用
* 操控辅助
  * [那维莱特转圈](https://bettergi.com/feats/macro/other.html#%E9%82%A3%E7%BB%B4%E8%8E%B1%E7%89%B9-%E8%BD%AC%E5%9C%88%E5%9C%88)：设置快捷键后，长按可以不断水平旋转视角（当然你也可以用来转草神）
  * [快速圣遗物强化](https://bettergi.com/feats/macro/other.html#%E5%9C%A3%E9%81%97%E7%89%A9%E4%B8%80%E9%94%AE%E5%BC%BA%E5%8C%96)：通过快速切换“详情”、“强化”页跳过圣遗物强化结果展示，快速+20
  * [商店一键购买](https://bettergi.com/feats/macro/other.html#%E4%B8%80%E9%94%AE%E8%B4%AD%E4%B9%B0)：可以快速以满数量购买商店中的物品，适合快速清空活动兑换，尘歌壶商店兑换等
* [**……**](https://bettergi.com/doc.html)

<div align="center">
  <img src="https://github.com/babalae/better-genshin-impact/assets/15783049/57ab7c3c-709a-4cf3-8f64-1c78764c364c"/>
  <p>自带一个遮罩窗口覆盖在游戏界面上，用于显示日志和图像识别结果</p>
</div>

## 截图

![0 39 1](https://github.com/user-attachments/assets/8fb0bfd9-e0db-4289-800f-1bc2efb221aa)


## 下载

> [!NOTE]
> 下载地址：[⚡Github 下载](https://github.com/babalae/better-genshin-impact/releases)
> 
> 不知道下载哪个？第一次使用？请看：[快速上手](https://bettergi.com/quickstart.html) ， 遇到问题请先看：[常见问题](https://bettergi.com/faq.html)

最新编译版本可以从自动构建中获取： [![](https://github.com/babalae/better-genshin-impact/actions/workflows/publish.yml/badge.svg)](https://github.com/babalae/better-genshin-impact/actions/workflows/publish.yml)

## 使用方法
由于图像识别比较吃性能，低配置电脑可能无法正常使用部分功能。

推荐的电脑配置至少能够中画质60帧流畅游玩原神，否则部分功能的使用体验会较差。

你的系统需要满足以下条件：
  * Windows 10 或更高版本的64位系统
  * [.NET 8 运行时](https://dotnet.microsoft.com/zh-cn/download/dotnet/latest/runtime) （没有的话，启动程序，系统会提示下载安装）

**⚠️注意：**
1. 窗口大小变化、切换游戏分辨率、切换显示器的时候请重启本软件。
2. 不支持任何画面滤镜（HDR、N卡滤镜等）。游戏亮度请保持默认。
3. 当前只支持 `16:9` 的分辨率，推荐在 `1920x1080` 窗口化游戏下使用。
4. **模拟操作部分可能被部分安全软件拦截，请加入白名单。已知360或者自定义规则WD会拦截部分类型的模拟点击**

**打开软件以后，在“启动”页选择好截图方式，点击启动按钮就可以享受 BetterGI 带来的便利了！**

详细使用指南请看：[快速上手](https://bettergi.com/quickstart.html)

具体功能效果与使用方式见：[文档](https://bettergi.com/doc.html)

## FAQ
* 为什么需要管理员权限？
  * 因为游戏是以管理员权限启动的，软件不以管理员权限启动的话没有权限模拟鼠标点击。
* 会不会封号？
  * 理论上不会被封。 **BetterGI 不会做出任何修改游戏文件、读写游戏内存等任何危害游戏本体的行为，单纯依靠视觉算法和模拟操作实现。** 但是mhy是自由的，用户条款上明确说明第三方软件/模拟操作是封号理由之一。当前方案还是存在被检测的可能。只能说请低调使用，请不要跳脸官方。
* [更多常见问题...](https://bettergi.com/faq.html)

## 致谢

本项目的完成离不开以下项目：
* [Yap](https://github.com/Alex-Beng/Yap)
* [genshin-woodmen](https://github.com/genshin-matrix/genshin-woodmen)
* [Fischless](https://github.com/genshin-matrix/Fischless)
* [MicaSetup](https://github.com/lemutec/MicaSetup)
* [cvAutoTrack](https://github.com/GengGode/cvAutoTrack)
* [genshin_impact_assistant](https://github.com/infstellar/genshin_impact_assistant)
* [HutaoFisher](https://github.com/myHuTao-qwq/HutaoFisher)
* [minimap](https://github.com/tignioj/minimap)
* [kachina-installer](https://github.com/YuehaiTeam/kachina-installer)

另外特别感谢 [@Lightczx](https://github.com/Lightczx) 和 [@emako](https://github.com/emako) 对本项目的指导与贡献

## 开发者

格式化：[CodeMaid.config](CodeMaid.config)、[Settings.XamlStyler](Settings.XamlStyler)；<br>

[如何编译项目？](BetterGenshinImpact/README.md)

## 许可证

![GPL-v3](https://www.gnu.org/graphics/gplv3-127x51.png)

## 问题反馈

提 [Issue](https://github.com/babalae/better-genshin-impact/issues) 或 QQ群[1051494685](https://qm.qq.com/q/TPQtZlgraU)

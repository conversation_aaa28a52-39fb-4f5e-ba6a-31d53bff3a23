﻿<ui:FluentWindow x:Class="BetterGenshinImpact.View.Windows.PromptDialog"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 Width="500"
                 Height="210"
                 MinWidth="400"
                 MinHeight="200"
                 ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
                 ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                 ExtendsContentIntoTitleBar="True"
                 FontFamily="{DynamicResource TextThemeFontFamily}"
                 ResizeMode="CanMinimize"
                 SizeToContent="Width"
                 WindowBackdropType="Mica"
                 WindowStartupLocation="CenterScreen"
                 WindowStyle="SingleBorderWindow"
                 mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="1" Margin="12">
            <ui:TextBlock Name="TxtQuestion" Margin="5" />
            <!-- <ui:TextBox Name="TxtResponse" Margin="5" /> -->
            <ContentControl Name="DynamicContent" Margin="5" />
            <Grid Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                
                <!-- 左下角按钮 -->
                <ui:Button Name="BtnLeftBottom"
                           Margin="5"
                           HorizontalAlignment="Left"
                           Appearance="Success"
                           Content="左下角按钮" 
                           Grid.Column="0" />
                
                <!-- 原有的右侧按钮 -->
                <StackPanel Grid.Column="1"
                            Orientation="Horizontal">
                    <ui:Button Name="BtnOk"
                               Margin="5"
                               Appearance="Primary"
                               Click="BtnOkClick"
                               Content="确定"
                               IsDefault="True" />
                    <ui:Button Name="BtnCancel"
                               Margin="5"
                               Click="BtnCancelClick"
                               Content="取消"
                               IsCancel="True" />
                </StackPanel>
            </Grid>
        </StackPanel>

        <ui:TitleBar Name="MyTitleBar" Grid.Row="0">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>

    </Grid>
</ui:FluentWindow>

<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:drawer="clr-namespace:BetterGenshinImpact.View.Controls.Drawer">
    
    <Style TargetType="{x:Type drawer:CustomDrawer}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type drawer:CustomDrawer}">
                    <Grid x:Name="PART_MainGrid">
                        <Border x:Name="PART_BackgroundOverlay"
                                Background="Black"
                                Opacity="0"/>
                        <Border x:Name="PART_DrawerContainer"
                                Background="{TemplateBinding DrawerBackground}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="0"
                                Effect="{DynamicResource ControlElevationBorderShadow}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
﻿<Page x:Class="BetterGenshinImpact.View.Pages.MacroSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:BetterGenshinImpact.View.Pages"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      Title="HomePage"
      d:DataContext="{d:DesignInstance Type=pages:MacroSettingsPageViewModel}"
      d:DesignHeight="950"
      d:DesignWidth="800"
      ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
      ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      FontFamily="{StaticResource TextThemeFontFamily}"
      Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      mc:Ignorable="d">
    <StackPanel Margin="42,16,42,12">
        <ui:TextBlock Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="辅助操控设置" />

        <!--  一键战斗宏  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Script16}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="一键宏（按角色）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="触发后会识别当前出战角色，并根据配置执行对应的宏"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.MacroConfig.CombatMacroEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="快捷键触发方式"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="按住时重复：按住时重复执行；触发：按下启动再按关闭"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="180"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding QuickFightMacroHotkeyMode}"
                              SelectedItem="{Binding Config.MacroConfig.CombatMacroHotkeyMode, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="宏配置"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        配置每个角色执行的宏，比如：胡桃A重跳，<Hyperlink Command="{Binding GoToOneKeyMacroUrlCommand}" Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看说明
                        </Hyperlink>
                    </ui:TextBlock>
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding EditAvatarMacroCommand}"
                               Content="前往设置" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="默认战斗宏编号"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="当角色的 macroPriority 设置为0时，使用此默认宏编号（1~5）"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="90"
                                  Margin="0,0,36,0"
                                  Text="{Binding Config.MacroConfig.CombatMacroPriority, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  FontTypography="Body"
                                  Text="角色个性化宏编号设置"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="上方宏配置支持为每个角色单独设置宏编号。在角色宏配置中设置 macroPriority 字段（1-5），设置为0则使用上面的默认战斗宏编号。"
                                  TextWrapping="Wrap" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  水龙王转圈圈  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Cursor24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="那维莱特 - 转圈圈"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="快速水平平移鼠标，需要配置快捷键进行触发"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,24,0"
                               Command="{Binding GoToHotKeyPageCommand}"
                               Content="绑定快捷键" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="移动鼠标距离"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="可以为负数，绝对值越大移动越快，请不要配置太大"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.MacroConfig.RunaroundMouseXInterval, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="移动鼠标间隔（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="尽量设置大于0的数字，配置完毕后请切换页签生效（其他配置也一样）"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.MacroConfig.RunaroundInterval, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  一键强化  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Cursor24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="快速强化圣遗物"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="快速跳过强化结果展示，需要配置快捷键进行触发"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,24,0"
                               Command="{Binding GoToHotKeyPageCommand}"
                               Content="绑定快捷键" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="强化的额外等待时间（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="高延迟下无法跳过强化结果显示时，需要延长这个时间配置"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  Margin="0,0,36,0"
                                  Maximum="1000"
                                  Minimum="0"
                                  ValidationMode="InvalidInputOverwritten"
                                  Value="{Binding Config.MacroConfig.EnhanceWaitDelay, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  一键购买  -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Cursor24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="一键购买"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="在物品购买/兑换页使用，从选中物品处开始，长按持续购买"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardControl.Header>
            <ui:Button Margin="0,0,36,0"
                       Command="{Binding GoToHotKeyPageCommand}"
                       Content="绑定快捷键" />
        </ui:CardControl>

        <!--  一键进出尘歌壶  -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Cursor24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="一键进出尘歌壶"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="一键自动打开背包，放置尘歌壶并进入"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardControl.Header>
            <ui:Button Margin="0,0,36,0"
                       Command="{Binding GoToHotKeyPageCommand}"
                       Content="绑定快捷键" />
        </ui:CardControl>

        <!--  一键确认/取消  -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Cursor24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="一键确认/取消"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="绑定快捷键到原神的确认/取消按钮"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardControl.Header>
            <ui:Button Margin="0,0,36,0"
                       Command="{Binding GoToHotKeyPageCommand}"
                       Content="绑定快捷键" />
        </ui:CardControl>



        <!--  长按空格等于连续按下空格  -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Keyboard24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="长按空格等于连续按下空格"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="轻松解除冻结，由于在水下存在长按的空格的场景，所以不推荐启用"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardControl.Header>
            <ui:ToggleSwitch Margin="0,0,36,0" IsChecked="{Binding Config.MacroConfig.SpacePressHoldToContinuationEnabled, Mode=TwoWay}" />
        </ui:CardControl>
        <!--  长按 F 等于连续按下 F  -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Keyboard24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="长按 F 等于连续按下 F "
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="快速拾取大量掉落物，有自动拾取的功能下，就显得比较鸡肋了"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardControl.Header>
            <ui:ToggleSwitch Margin="0,0,36,0" IsChecked="{Binding Config.MacroConfig.FPressHoldToContinuationEnabled, Mode=TwoWay}" />
        </ui:CardControl>

        <!--  一键锻造  -->
        <!--<ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Cursor24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="快速锻造【开发中】" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="满收获队列时使用，自动收取并重新锻造水晶块" />
                </Grid>
            </ui:CardControl.Header>
            <ui:Button
                Margin="0,0,36,0"
                Content="绑定快捷键" />
        </ui:CardControl>-->
    </StackPanel>
</Page>
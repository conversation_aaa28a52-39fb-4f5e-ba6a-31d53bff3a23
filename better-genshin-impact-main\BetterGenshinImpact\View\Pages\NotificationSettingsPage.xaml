﻿<Page x:Class="BetterGenshinImpact.View.Pages.NotificationSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      Title="NotificationSettingsPage"
      d:DataContext="{d:DesignInstance Type=pages:NotificationSettingsPageViewModel}"
      d:DesignHeight="1500"
      d:DesignWidth="600"
      ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
      ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      FontFamily="{StaticResource TextThemeFontFamily}"
      Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      mc:Ignorable="d">

    <StackPanel Margin="42,16,42,12">
        <ui:TextBlock Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="通知设置" />

        <!--  全局通知设置  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Alert24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="全局通知设置"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="影响下方所有通知的设置"
                                  TextWrapping="Wrap" />
                    <!--<ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.WebhookEnabled, Mode=TwoWay}" />-->
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="通知时包含截图"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="总是在通知中包含截图"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.NotificationConfig.IncludeScreenShot, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="是否允许 JS 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="开启时允许 JS 脚本发送通知"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.NotificationConfig.JsNotificationEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="需要通知的事件"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="英文逗号分割，为空为全部通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="220"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.NotificationEventSubscribe, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  Webhook  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon CloudArrowUp24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 Webhook"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="Webhook 相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.WebhookEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="Webhook 端点"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 Webhook 端点"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.WebhookEndpoint, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="发送对象"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写发送对象"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.WebhookSendTo, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试载荷"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.Column="1"
                               Grid.RowSpan="2"
                               Margin="0,0,36,0"
                               HorizontalAlignment="Right"
                               Command="{Binding TestWebhookCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!--  WebSocket  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon CloudLink24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 WebSocket"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="WebSocket 相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.WebSocketNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="WebSocket 端点"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 WebSocket 端点"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.WebSocketEndpoint, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试 WebSocket 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestWebSocketNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- Windows 通知 -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Alert28}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 Windows 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="Windows 通知别与游戏界面重叠，否则易误点通知"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.WindowsUwpNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试 Windows 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestWindowsUwpNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- 飞书通知 -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Send20}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用飞书通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="飞书通知相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.FeishuNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="飞书通知地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写飞书通知地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.FeishuWebhookUrl, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="飞书AppId"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="若填写AppId、AppSecret则发送图片"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.FeishuAppId, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="飞书AppSecret"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="若填写AppId、AppSecret则发送图片"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.FeishuAppSecret, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试飞书通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestFeishuNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- OneBot 通知 -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Bot24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 OneBot 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="OneBot 通知相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.OneBotNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="OneBot 请求地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 OneBot 请求地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.OneBotEndpoint, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="QQ 号"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写接收消息的 QQ 号"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.OneBotUserId, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="群号"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写接收消息的群号"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.OneBotGroupId, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="Token"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 OneBot Token（可选）"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.OneBotToken, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试 OneBot 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestOneBotNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- 企业微信通知 -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon ChatMultiple24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用企业微信通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="企业微信通知相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.WorkweixinNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="企业微信通知地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写企业微信通知地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.WorkweixinWebhookUrl, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试企业微信通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestWorkWeixinNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  Email通知  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon 	ChatMail20}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用邮箱通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="邮箱相关设置（账号密码完全保存于本地）"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.EmailNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="SMTP 服务器"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 SMTP 服务器"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.SmtpServer, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="SMTP 服务器端口"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 SMTP 服务器端口，一般为：587"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                MinWidth="60"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.SmtpPort, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="SMTP 用户名"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 SMTP 用户名"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.SmtpUsername, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="SMTP 密码"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 SMTP 密码"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.SmtpPassword, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="发件人邮箱"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写发件人邮箱"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.FromEmail, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="发件人姓名"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写发件人姓名"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.FromName, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="收件人邮箱"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写收件人邮箱"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.ToEmail, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试邮箱通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestEmailNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!--  Bark通知  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon CommentArrowRight24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 Bark 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="Bark ios 推送通知"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.BarkNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="Bark API 端点"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 Bark API 端点,例如：api.day.app"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.BarkApiEndpoint, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="设备 Key"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="多个设备使用英文逗号、分号或空格分隔"
                                  TextWrapping="Wrap" />

                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.BarkDeviceKeys, Mode=TwoWay}"
                                TextWrapping="Wrap" />
                </Grid>

                <!-- 高级参数 -->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="推送级别"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="修改推送级别"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              SelectedItem="{Binding Config.NotificationConfig.BarkLevel}"
                              SelectedValuePath="Tag"
                              Margin="0,0,36,0"
                              MinWidth="120">
                        <ComboBoxItem Content="passive（通知但不提醒）" Tag="passive" />
                        <ComboBoxItem Content="critical（重要通知）" Tag="critical" />
                        <ComboBoxItem Content="active（默认通知）" Tag="active" />
                        <ComboBoxItem Content="sensitive（专注通知）" Tag="timeSensitive" />
                    </ComboBox>
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="通知声音"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="选择通知声音"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              SelectedValuePath="Content"
                              SelectedValue="{Binding Config.NotificationConfig.BarkSound}"
                              Margin="0,0,36,0"
                              MinWidth="120">
                        <ComboBoxItem Content="minuet" />
                        <ComboBoxItem Content="silence" />
                        <ComboBoxItem Content="bell" />
                    </ComboBox>
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="通知分组"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="按组显示在通知中心"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.BarkGroup, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="保存推送历史"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="选择是否保存推送历史"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              MinWidth="180"
                              MaxWidth="800"
                              Margin="0,0,36,0"
                              SelectedValue="{Binding Config.NotificationConfig.BarkIsArchive, Mode=TwoWay}"
                              SelectedValuePath="Tag">
                        <ComboBoxItem Content="是" Tag="1" />
                        <ComboBoxItem Content="否" Tag="0" />
                    </ComboBox>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="加密密钥"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="推送内容加密密钥"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.BarkCiphertext, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <!-- 测试按钮 -->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试 Bark 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestBarkNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!--  Telegram通知  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Bot24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 Telegram 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="Telegram 机器人相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.TelegramNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="机器人 Token"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 Telegram 机器人的 Token"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.TelegramBotToken, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="聊天 ID"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写接收消息的聊天 ID"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.TelegramChatId, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="API 基础 URL（可选）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="留空使用官方 API，或填写第三方 API 地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.TelegramApiBaseUrl, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用代理"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="是否启用代理连接"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.NotificationConfig.TelegramProxyEnabled, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="代理地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="格式：http://127.0.0.1:7890"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.TelegramProxyUrl, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>

                <!-- 测试按钮 -->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试 Telegram 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestTelegramNotificationCommand}"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!--  xxtui 信息推送通知  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Chat24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 xxtui 信息推送"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="xxtui 信息推送通知相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.XxtuiNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="API密钥"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写信息推送通知API密钥"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"

                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.XxtuiApiKey, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="通知来源"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写信息推送通知来源"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.XxtuiFrom, Mode=TwoWay}"
                                PlaceholderText="Better原神"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="通知渠道"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写信息推送通知渠道"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.XxtuiChannels, Mode=TwoWay}"
                                PlaceholderText="WX_MP,WX_QY_ROBOT,DING_ROBOT,BARK"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试信息推送通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestXxtuiNotificationCommand}"
                               CommandParameter="xxtuiNotifier"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!--  钉钉机器人  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Bot24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用钉钉机器人通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="钉钉机器人通知相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.DingDingwebhookNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="钉钉机器人通知地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写钉钉机器人通知地址"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.DingdingWebhookUrl, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <!-- 添加密钥输入字段 -->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="钉钉机器人密钥"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写钉钉机器人密钥"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.DingDingSecret, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试钉钉机器人通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestDingDingWebhookNotificationCommand}"
                               CommandParameter="DingDingWebhook"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!--  Discord Webhook  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Bot24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用 Discord Webhook 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="Discord Webhook 通知相关设置"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.NotificationConfig.DiscordWebhookNotificationEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="Webhook 网址"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="填写 Discord 提供的 Webhook 网址"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.DiscordWebhookUrl, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="用户名"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="讯息显示的名称，为空时使用 Discord 中预先设定的名字"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.DiscordWebhookUsername, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="3*" />
                        <ColumnDefinition Width="2*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="头像网址"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="讯息显示的头像，为空时使用 Discord 中预先设定的头像"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.NotificationConfig.DiscordWebhookAvatarUrl, Mode=TwoWay}"
                                TextWrapping="NoWrap" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="截图编码"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="PNG 无损，JPEG 快速压缩，WebP 档案小，按需选择"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="100"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding DiscordImageEncoderNames}"
                              SelectedItem="{Binding Config.NotificationConfig.DiscordWebhookImageEncoder, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试 Discord Webhook 通知"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="发送测试通知"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding TestDiscordWebhookNotificationCommand}"
                               CommandParameter="DiscordWebhook"
                               Content="发送" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
    </StackPanel>
</Page>
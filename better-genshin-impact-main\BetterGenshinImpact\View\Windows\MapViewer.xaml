﻿<ui:FluentWindow x:Class="BetterGenshinImpact.View.Windows.MapViewer"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 xmlns:windows="clr-namespace:BetterGenshinImpact.ViewModel.Windows"
                 Title="地图"
                 Width="512"
                 Height="512"
                 d:DataContext="{d:DesignInstance Type=windows:MapViewerViewModel}"
                 ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
                 ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                 ExtendsContentIntoTitleBar="True"
                 FontFamily="{DynamicResource TextThemeFontFamily}"
                 ResizeMode="CanMinimize"
                 Topmost="True"
                 WindowBackdropType="Mica"
                 WindowStartupLocation="CenterScreen"
                 WindowStyle="SingleBorderWindow"
                 mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <Canvas Grid.Row="1">
            <ui:Image Source="{Binding MapBitmap, Mode=OneWay}" Stretch="Uniform" />
        </Canvas>


        <ui:TitleBar Title="地图" Grid.Row="0">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>

    </Grid>
</ui:FluentWindow>
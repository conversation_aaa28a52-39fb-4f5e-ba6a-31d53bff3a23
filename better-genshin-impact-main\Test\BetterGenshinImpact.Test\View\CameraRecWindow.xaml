﻿<Window x:Class="BetterGenshinImpact.Test.View.CameraRecWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:BetterGenshinImpact.Test"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:oxy="http://oxyplot.org/wpf"
        xmlns:view="clr-namespace:BetterGenshinImpact.Test.View"
        Title="MainWindow"
        Width="400"
        Height="800"
        mc:Ignorable="d">
    <Window.DataContext>
        <view:CameraRecViewModel />
    </Window.DataContext>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>
        <oxy:PlotView Grid.Row="0" Model="{Binding LeftModel}" />
        <oxy:PlotView Grid.Row="1" Model="{Binding RightModel}" />
        <oxy:PlotView Grid.Row="2" Model="{Binding AllModel}" />
    </Grid>
</Window>

﻿<ui:FluentWindow x:Class="BetterGenshinImpact.View.Windows.MapPathingDevWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 xmlns:windows="clr-namespace:BetterGenshinImpact.ViewModel.Windows"
                 xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                 d:DataContext="{d:DesignInstance Type=windows:MapPathingDevViewModel}"
                 ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
                 ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                 Width="320"
                 Height="260"
                 mc:Ignorable="d">
    <StackPanel>
        <ui:TitleBar Title="地图追踪开发者工具">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>

        <StackPanel Margin="12">
            <ui:TextBlock Margin="5">(下方地图选择会影响路径录制所在的独立地图)</ui:TextBlock>
            <ui:TextBlock Margin="5">请选择地图：</ui:TextBlock>
            <ComboBox ItemsSource="{Binding MapTypeItems}"
                      DisplayMemberPath="DisplayName"
                      SelectedValuePath="EnumName"
                      SelectedValue="{Binding DevConfig.RecordMapName, Mode=TwoWay}"
                      Margin="5">
                <!--<b:Interaction.Triggers>
                    <b:EventTrigger EventName="SelectionChanged">
                        <b:InvokeCommandAction Command="{Binding DropDownChangedCommand}"
                                               CommandParameter="GeniusInvocation" />
                    </b:EventTrigger>
                </b:Interaction.Triggers>-->
            </ComboBox>
            <ui:Button Margin="5"
                       Command="{Binding OpenMapViewerCommand}"
                       Content="查看实时追踪地图" />

            <ui:Button Margin="5"
                       Command="{Binding OpenMapEditorCommand}"
                       Content="录制编辑器" />
        </StackPanel>
    </StackPanel>
</ui:FluentWindow>
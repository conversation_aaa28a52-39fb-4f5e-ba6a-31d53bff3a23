﻿<ui:FluentWindow x:Class="BetterGenshinImpact.View.Windows.JsonMonoDialog"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:codeEditor="clr-namespace:BetterGenshinImpact.View.Controls.CodeEditor"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 Title="JSON 编辑器"
                 Width="600"
                 Height="600"
                 d:Background="#D2D2D2"
                 ExtendsContentIntoTitleBar="True"
                 FontFamily="{DynamicResource TextThemeFontFamily}"
                 Tag="JsonMonoDialog"
                 WindowBackdropType="Mica"
                 WindowStartupLocation="CenterOwner"
                 mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/View/Controls/CodeEditor/CodeBox.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Grid>
        <Grid Margin="8,48,8,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Grid Grid.Row="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <codeEditor:JsonCodeBox x:Name="JsonCodeBox"
                                        Margin="0,8,0,0"
                                        Code="{Binding JsonText, Mode=OneTime}" />
            </Grid>
            <Grid Grid.Row="1" Margin="0,8,0,8">
                <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                    <Button MinWidth="90"
                            Margin="8,0,8,0"
                            Command="{Binding SaveCommand}"
                            Content="保存" />
                    <Button MinWidth="90"
                            Command="{Binding CloseCommand}"
                            Content="关闭" />
                </StackPanel>
            </Grid>
        </Grid>
        <ui:TitleBar Title="JSON 编辑器">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>
    </Grid>
</ui:FluentWindow>
﻿<UserControl x:Class="BetterGenshinImpact.View.Pages.View.ScriptGroupConfigView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages.View"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             Width="800"
             d:DataContext="{d:DesignInstance Type=pages:ScriptGroupConfigViewModel}"
             d:DesignHeight="850"
             d:DesignWidth="800"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             mc:Ignorable="d">
    <ScrollViewer Height="800"
                  HorizontalScrollBarVisibility="Auto"
                  VerticalScrollBarVisibility="Auto">
        <StackPanel Width="700" Margin="42,16,42,12">

            <ui:CardExpander Margin="0,0,0,12"
                             ContentPadding="0"
                             Icon="{ui:SymbolIcon Map24}"
                             IsExpanded="True">
                <ui:CardExpander.Header>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="地图追踪行走配置（不启用则使用地图追踪内的条件配置）"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      TextWrapping="Wrap">
                            强制修改行走途中配置开盾/回血等角色在队伍内的编号
                        </ui:TextBlock>
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         Margin="0,0,24,0"
                                         IsChecked="{Binding PathingConfig.Enabled, Mode=TwoWay}" />
                    </Grid>
                </ui:CardExpander.Header>
                <StackPanel>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="是否开启自动拾取"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="推荐开启以拾取路线中的掉落物、采集物"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding PathingConfig.AutoPickEnabled}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="切换到队伍的名称（为空则不切换）"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="队伍名称是你在游戏中手动设置的队伍名称文字"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="200"
                                    Text="{Binding PathingConfig.PartyName}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="切换队伍前强制前往安全的神像区域"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="前往七天神像切换队伍可以避免切换队伍时位于异常状态导致切换失败"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding PathingConfig.IsVisitStatueBeforeSwitchParty}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="【生存位】在队伍内的编号"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="开盾/回血的角色位置，推荐琦良良、莱依拉"
                                      TextWrapping="Wrap" />
                        <ComboBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="100"
                                  ItemsSource="{Binding PathingConfig.AvatarIndexList}"
                                  SelectedItem="{Binding PathingConfig.GuardianAvatarIndex}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="【生存位】释放元素战技的间隔（秒）"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="可能要填的时间略大于实际技能CD时间"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="120"
                                    Text="{Binding PathingConfig.GuardianElementalSkillSecondInterval}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="【生存位】元素战技是否长按"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="钟离长按开盾、琦良良长按变猫等"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding PathingConfig.GuardianElementalSkillLongPress}"
                                         OffContent="短按"
                                         OnContent="长按" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="【行走位】主要行走的角色在队伍内的编号"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="推荐和生存位相同，没有特殊需求无需配置"
                                      TextWrapping="Wrap" />
                        <ComboBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="100"
                                  ItemsSource="{Binding PathingConfig.AvatarIndexList}"
                                  SelectedItem="{Binding PathingConfig.MainAvatarIndex}" />
                    </Grid>

                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="使用小道具的间隔时间（毫秒）"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="在移动期间自动使用小道具，填0为不使用"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="100"
                                    Text="{Binding PathingConfig.UseGadgetIntervalMs, Mode=TwoWay}"
                                    TextWrapping="NoWrap" />
                    </Grid>

                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="只在传送点时恢复"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="只有路径在传送点时，才会去七天神像恢复"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding PathingConfig.OnlyInTeleportRecover, Mode=TwoWay}" />

                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="不在某时执行"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="当执行完一个路线后，如果时间为当前配置的时间（范围：0-23），则此地图追踪任务后续都将都跳过，适用于连续执行的兜底任务，例如想通宵挂机，并且在4点后，开始执行新的任务。"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="100"
                                    Text="{Binding PathingConfig.SkipDuring, Mode=TwoWay}"
                                    TextWrapping="NoWrap" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="不在连续任务中显示"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="点击连续执行时，不显示该配置组"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding PathingConfig.HideOnRepeat, Mode=TwoWay}" />

                    </Grid>
                                       <!-- 执行周期配置 -->
                    <Grid Margin="16">

                        <ui:CardExpander Margin="0,0,36,12"
                                         ContentPadding="0"
                                         IsExpanded="False">

                            <ui:CardExpander.Header>
                                <Grid>

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="执行周期配置"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="以天为单位来决定是否执行此配置组"
                                                  TextWrapping="Wrap" />
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.RowSpan="2"
                                                     Grid.Column="1"
                                                     Margin="0,0,24,0"
                                                     IsChecked="{Binding PathingConfig.TaskCycleConfig.Enable, Mode=TwoWay}" />
                                </Grid>
                            </ui:CardExpander.Header>
                            <StackPanel>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="分界时间"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="分界时间，一般填0或4，如果填0则当天从0点开始算，如果填4，则当天从凌晨4点开始算，小于4点算前一天。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.TaskCycleConfig.BoundaryTime}" />
                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="周期"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="不同材料有不同的周期，按需配置，如矿石类是3、突破材料是2，或按照自已想几天执行一次配置即可。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.TaskCycleConfig.Cycle}" />
                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="执行序号"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="执行周期序号，按时间戳对应的天数(会根据分界时间修正)，对周期求余值加1，得出的值和配置执一致就会执行，否则跳过任务。可以理解为周期内第几天执行。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.TaskCycleConfig.Index}" />
                                </Grid>
                                 <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="计算当天执行序号"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="点击计算按钮，可计算出当天执行序号（今天是周期内的第几天）。"
                                                  TextWrapping="Wrap" />
                                    <ui:Button Grid.Row="0"
                                               Grid.RowSpan="2"
                                               Grid.Column="1"
                                               Margin="0,0,12,0"
                                               Command="{Binding GetExecutionOrderCommand}"
                                               Content="计算"
                                               Icon="{ui:SymbolIcon Archive24}" />
                                   </Grid>
                               
                            </StackPanel>
                        </ui:CardExpander>

                    </Grid>
                                       
                                       
                                       <!-- 跳过执行配置 -->
                    <Grid Margin="16">

                        <ui:CardExpander Margin="0,0,36,12"
                                         ContentPadding="0"
                                         IsExpanded="False">

                            <ui:CardExpander.Header>
                                <Grid>

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="跳过执行配置"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="跳过指定时间内完成的调度器任务（js等未抛出异常则算成功，路径追踪任务，走完最后路径才算成功）的配置"
                                                  TextWrapping="Wrap" />
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.RowSpan="2"
                                                     Grid.Column="1"
                                                     Margin="0,0,24,0"
                                                     IsChecked="{Binding PathingConfig.TaskCompletionSkipRuleConfig.Enable, Mode=TwoWay}" />
                                </Grid>
                            </ui:CardExpander.Header>
                            <StackPanel>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="跳过执行策略"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="根据不同的策略来跳过任务"
                                                  TextWrapping="Wrap" />
                                    <ComboBox Grid.Row="0"
                                              Grid.RowSpan="2"
                                              Grid.Column="1"
                                              MinWidth="100"
                                              SelectedValuePath="Key"
                                              DisplayMemberPath="Value"
                                              ItemsSource="{Binding SkipPolicySource}"
                                              SelectedValue="{Binding PathingConfig.TaskCompletionSkipRuleConfig.SkipPolicy}" />
                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="分界时间"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="分界时间，一般填0或4，如果填0则当天从0点开始算，如果填4，则当天从凌晨4点开始算，小于4点算前一天，填写小于0的数或大于23，如-1，则不启用，适用于考虑固定刷新时间，且一天内不重复执行的情况。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.TaskCompletionSkipRuleConfig.BoundaryTime}" />
                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="和上次运行的间隔秒数"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="和上一次运行时间间隔时间小于配置时间，则跳过执行，小于0时不启用，出于精度考虑，这里使用秒为单位。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.TaskCompletionSkipRuleConfig.LastRunGapSeconds}" />
                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="间隔时间计算参照"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="根据配置决定，间隔时间是根据开始时间算，还是根据结束时间算"
                                                  TextWrapping="Wrap" />
                                    <ComboBox Grid.Row="0"
                                              Grid.RowSpan="2"
                                              Grid.Column="1"
                                              MinWidth="100"
                                              SelectedValuePath="Key"
                                              DisplayMemberPath="Value"
                                              ItemsSource="{Binding ReferencePointSource}"
                                              SelectedValue="{Binding PathingConfig.TaskCompletionSkipRuleConfig.ReferencePoint}" />
                                </Grid>
                            </StackPanel>
                        </ui:CardExpander>

                    </Grid>        
                                       <!-- 优先执行其他配置组 -->
                     <Grid Margin="16">

                        <ui:CardExpander Margin="0,0,36,12"
                                         ContentPadding="0"
                                         IsExpanded="False">

                            <ui:CardExpander.Header>
                                <Grid>

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="优先执行其他配置组"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="每个任务开始前，会先检查优先执行的其他配置组，是否有任务可以执行，如果可以执行，则会先执行其他配置组的任务，所配置的其他任务组，必须开启跳过执行配置，不支持套娃，才会生效。可用于卡时间、优先材料等提高收益。"
                                                  TextWrapping="Wrap" />
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.RowSpan="2"
                                                     Grid.Column="1"
                                                     Margin="0,0,24,0"
                                                     IsChecked="{Binding PathingConfig.PreExecutionPriorityConfig.Enabled, Mode=TwoWay}" />
                                </Grid>
                            </ui:CardExpander.Header>
                            <StackPanel>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="配置组名称"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="配置组名，多个用逗号分隔，只有开启跳过执行配置，才会生效。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.PreExecutionPriorityConfig.GroupNames}" />
                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="额外执行次数"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="需要优先的任务，在此配置组执行时，最多尝试的额外次数（失败或者CD过短导致依旧满足条件），防止无限循环，另外，此计数关闭任务，继续执行会清空。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.PreExecutionPriorityConfig.MaxRetryCount}" />
                                </Grid>

                            </StackPanel>
                        </ui:CardExpander>

                    </Grid>   
                                       
                </StackPanel>
            </ui:CardExpander>
            <ui:CardExpander Margin="0,0,0,12"
                             ContentPadding="0"
                             IsExpanded="True">
                <ui:CardExpander.Icon>
                    <ui:FontIcon Glyph="&#xf71d;" Style="{StaticResource FaFontIconStyle}" />
                </ui:CardExpander.Icon>
                <ui:CardExpander.Header>
                    <Grid>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="战斗配置"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      TextWrapping="Wrap">
                            需开启上方地图追踪配置，开启此项可制定战斗配置，关闭此项，则用于独立任务中的配置
                        </ui:TextBlock>
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         Margin="0,0,24,0"
                                         IsChecked="{Binding PathingConfig.AutoFightEnabled, Mode=TwoWay}">
                            <b:Interaction.Triggers>
                                <b:EventTrigger EventName="Checked">
                                    <b:InvokeCommandAction Command="{Binding AutoFightEnabledCheckedCommand}" />
                                </b:EventTrigger>
                            </b:Interaction.Triggers>
                        </ui:ToggleSwitch>
                        <!--    -->
                    </Grid>
                </ui:CardExpander.Header>
                <StackPanel>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="选择战斗策略"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="用于战斗"
                                      TextWrapping="Wrap" />
                        <ui:Button Grid.Row="0"
                                   Grid.RowSpan="2"
                                   Grid.Column="1"
                                   Margin="0,0,12,0"
                                   Command="{Binding OpenLocalScriptRepoCommand}"
                                   Content="脚本仓库"
                                   Icon="{ui:SymbolIcon Archive24}" />
                        <ui:Button Grid.Row="0"
                                   Grid.RowSpan="2"
                                   Grid.Column="2"
                                   Margin="0,0,12,0"
                                   Command="{Binding OpenFightFolderCommand}"
                                   Content="打开目录" />
                        <ComboBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="3"
                                  Width="180"
                                  Margin="0,0,36,0"
                                  ItemsSource="{Binding AutoFightViewModel.CombatStrategyList}"
                                  SelectedItem="{Binding PathingConfig.AutoFightConfig.StrategyName, Mode=TwoWay}">
                            <b:Interaction.Triggers>
                                <b:EventTrigger EventName="DropDownOpened">
                                    <b:InvokeCommandAction Command="{Binding StrategyDropDownOpenedCommand}" CommandParameter="Combat" />
                                </b:EventTrigger>
                            </b:Interaction.Triggers>
                        </ComboBox>
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="根据技能CD优化出招人员"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="根据填入人或人和cd，来决定当此人元素战技cd未结束时，跳过此人出招，来优化战斗流程，可填入人名或人名数字（用逗号分隔），多种用分号分隔，例如:白术;钟离,12;，如果人名，则用内置cd检查（或填入数字也小于0），如果是人名和数字，则把数字当做出招cd(秒)。"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="180"
                                    MaxWidth="800"
                                    Margin="0,0,36,0"
                                    Text="{Binding PathingConfig.AutoFightConfig.ActionSchedulerByCd, Mode=TwoWay}"
                                    TextWrapping="Wrap" />
                    </Grid>
                    <Grid Margin="16">

                        <ui:CardExpander Margin="0,0,36,12"
                                         ContentPadding="0"
                                         IsExpanded="False">

                            <ui:CardExpander.Header>
                                <Grid>

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="自动检测战斗结束"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="检测到战斗已经结束的情况下，停止自动战斗功能"
                                                  TextWrapping="Wrap" />
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.RowSpan="2"
                                                     Grid.Column="1"
                                                     Margin="0,0,24,0"
                                                     IsChecked="{Binding PathingConfig.AutoFightConfig.FightFinishDetectEnabled, Mode=TwoWay}" />
                                </Grid>
                            </ui:CardExpander.Header>
                            <StackPanel>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Grid Margin="16">

                                        <ui:CardExpander Margin="0,0,0,12"
                                                         ContentPadding="0"
                                                         IsExpanded="False">

                                            <ui:CardExpander.Header>
                                                <Grid>

                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto" />
                                                        <RowDefinition Height="Auto" />
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*" />
                                                        <ColumnDefinition Width="Auto" />
                                                    </Grid.ColumnDefinitions>
                                                    <ui:TextBlock Grid.Row="0"
                                                                  Grid.Column="0"
                                                                  FontTypography="Body"
                                                                  Text="更快检查结束战斗"
                                                                  TextWrapping="Wrap" />
                                                    <ui:TextBlock Grid.Row="1"
                                                                  Grid.Column="0"
                                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                  Text="快速检查战斗结束，在一轮脚本中，可以每隔一定秒数（默认为5）或指定角色操作后，去检查（在每个角色完成该轮脚本时）。"
                                                                  TextWrapping="Wrap" />
                                                    <ui:ToggleSwitch Grid.Row="0"
                                                                     Grid.RowSpan="2"
                                                                     Grid.Column="1"
                                                                     Margin="0,0,36,0"
                                                                     IsChecked="{Binding PathingConfig.AutoFightConfig.FinishDetectConfig.FastCheckEnabled, Mode=TwoWay}" />
                                                </Grid>
                                            </ui:CardExpander.Header>
                                            <StackPanel>
                                                <Grid Margin="16">
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto" />
                                                        <RowDefinition Height="Auto" />
                                                    </Grid.RowDefinitions>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*" />
                                                        <ColumnDefinition Width="Auto" />
                                                    </Grid.ColumnDefinitions>
                                                    <ui:TextBlock Grid.Row="0"
                                                                  Grid.Column="0"
                                                                  FontTypography="Body"
                                                                  Text="更快检查结束战斗参数"
                                                                  TextWrapping="Wrap" />
                                                    <ui:TextBlock Grid.Row="1"
                                                                  Grid.Column="0"
                                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                                  Text="快速检查战斗结束的参数，可填入数字和人名，多种用分号分隔，例如:5;白术;钟离;，如果是数字（小于等于0则不会根据时间去检查，单位为秒），则指定检查间隔，如果是人名，则该角色执行一轮操作后进行检查。同时每轮结束后检查不变。"
                                                                  TextWrapping="Wrap" />
                                                    <ui:TextBox Grid.Row="0"
                                                                Grid.RowSpan="2"
                                                                Grid.Column="1"
                                                                MinWidth="180"
                                                                MaxWidth="800"
                                                                Margin="0,0,36,0"
                                                                Text="{Binding PathingConfig.AutoFightConfig.FinishDetectConfig.FastCheckParams, Mode=TwoWay}"
                                                                TextWrapping="Wrap" />
                                                </Grid>
                                            </StackPanel>
                                        </ui:CardExpander>
                                    </Grid>

                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="检查战斗结束的延时"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="检查战斗结束的延时，不同角色招式结束后的延时不一定相同，默认为1.5秒。也可以指定特定角色之后延时多少秒检查，未指定角色名，则默认为该值。格式如：2.5;白术,1.5;钟离,1.0;"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.AutoFightConfig.FinishDetectConfig.CheckEndDelay}" />
                                </Grid>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="按键触发后检查延时"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="按下切换队伍后去检查屏幕色块的延时，默认为0.45秒。若频繁误判可以适当提高这个值，比如到0.75。确保这个延时不会真的把队伍配置界面切出来。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Text="{Binding PathingConfig.AutoFightConfig.FinishDetectConfig.BeforeDetectDelay}" />
                                </Grid>

                            </StackPanel>
                        </ui:CardExpander>

                    </Grid>
                    <Grid Margin="16,10,52,0">

                        <ui:CardExpander Margin="0,0,0,12"
                                         ContentPadding="0"
                                         IsExpanded="False">

                            <ui:CardExpander.Header>
                                <Grid>

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="自动拾取掉落物"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="战斗结束后尽可能拾取周围掉落物（与万叶配合更佳）"
                                                  TextWrapping="Wrap" />
                                    <ui:ToggleSwitch Grid.Row="0"
                                                     Grid.RowSpan="2"
                                                     Grid.Column="1"
                                                     Margin="0,0,36,0"
                                                     IsChecked="{Binding PathingConfig.AutoFightConfig.PickDropsAfterFightEnabled}" />
                                </Grid>
                            </ui:CardExpander.Header>
                            <StackPanel>
                                <Grid Margin="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <ui:TextBlock Grid.Row="0"
                                                  Grid.Column="0"
                                                  FontTypography="Body"
                                                  Text="自动拾取掉落物时长"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBlock Grid.Row="1"
                                                  Grid.Column="0"
                                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                  Text="单位为秒。0表示不自动拾取掉落物。"
                                                  TextWrapping="Wrap" />
                                    <ui:TextBox Grid.Row="0"
                                                Grid.RowSpan="2"
                                                Grid.Column="1"
                                                MinWidth="120"
                                                Margin="0,0,36,0"
                                                Text="{Binding PathingConfig.AutoFightConfig.PickDropsAfterFightSeconds}" />
                                </Grid>
                            </StackPanel>
                        </ui:CardExpander>
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="战斗结束后执行万叶长E"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="战斗结束后，如果存在枫原万叶，则战斗结束后执行万叶长E，使用该角色捡材料"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         Margin="0,0,36,0"
                                         IsChecked="{Binding PathingConfig.AutoFightConfig.KazuhaPickupEnabled, Mode=TwoWay}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="拾取战斗人次阈值"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="拾取战斗人次阈值,当战斗人次小于一定次数，就结束战斗情况下，不触发拾取掉落物和万叶拾取配置，只有不小于2时才生效。"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="120"
                                    Margin="0,0,36,0"
                                    Text="{Binding PathingConfig.AutoFightConfig.BattleThresholdForLoot}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="只拾取精英掉落模式"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="需要路径文件支持，需勾选区分怪物拾取才生效，可通过仓库标签查看，精英怪物可以在点位的更多》扩展配置》怪物标签中，可标记。只拾取标记为精英或传奇的点位。非精英允许自动拾取：战斗过程中掉落脚下的可以自动拾取，但不会执行万叶拾取和自动拾取配置逻辑。非精英关闭拾取：战斗过程中掉落到脚下的也不会自动拾取。"
                                      TextWrapping="Wrap" />
                        <ComboBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="100"
                                  SelectedValuePath="Key"
                                  DisplayMemberPath="Value"
                                  ItemsSource="{Binding OnlyPickEliteDropsSource}"
                                  SelectedValue="{Binding PathingConfig.AutoFightConfig.OnlyPickEliteDropsMode}" />
                    </Grid>

                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="万叶拾取队伍名"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="战斗结束后，如果不存在万叶，则切换至存在万叶的队伍（基于开启万叶拾取情况下，并且填写队伍名才会切换），适用于需要多一个人刷经验时，替换调万叶的位置，战斗策略建议兼容两个队伍的，以防止队伍切换不成功也能继续战斗。"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="120"
                                    Margin="0,0,36,0"
                                    Text="{Binding PathingConfig.AutoFightConfig.KazuhaPartyName}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="战斗超时（秒）"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="当战斗超过一定时间后，自动停止战斗"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="120"
                                    Margin="0,0,36,0"
                                    Text="{Binding PathingConfig.AutoFightConfig.Timeout}" />
                    </Grid>
                </StackPanel>
            </ui:CardExpander>
            <ui:CardExpander Margin="0,0,0,12"
                             ContentPadding="0"
                             Icon="{ui:SymbolIcon FlashSettings24}"
                             IsExpanded="True">
                <ui:CardExpander.Header>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="Shell执行配置"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      TextWrapping="Wrap">
                            在本组中执行Shell命令会使用这里的配置。如关闭此项，则使用默认配置。
                        </ui:TextBlock>
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         Margin="0,0,24,0"
                                         IsChecked="{Binding ScriptGroupConfig.EnableShellConfig, Mode=TwoWay}" />
                    </Grid>
                </ui:CardExpander.Header>
                <StackPanel>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="禁用Shell任务"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="如果打开，则Shell任务会被跳过执行"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding ShellConfig.Disable}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="Shell执行超时"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="最长等待命令返回的时间，单位秒。如果时间&lt;=0则不等待，直接返回。"
                                      TextWrapping="Wrap" />
                        <ui:TextBox Grid.Row="0"
                                    Grid.RowSpan="2"
                                    Grid.Column="1"
                                    MinWidth="120"
                                    Text="{Binding ShellConfig.Timeout}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="隐藏命令执行窗口"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="推荐启用，不然会有一个大黑框。"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding ShellConfig.NoWindow}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="在Log中记录命令输出"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="打开的话若命令有输出会在Log中显示，关闭则不显示。(只有命令执行成功且有输出才会显示)"
                                      TextWrapping="Wrap" />
                        <ui:ToggleSwitch Grid.Row="0"
                                         Grid.RowSpan="2"
                                         Grid.Column="1"
                                         IsChecked="{Binding ShellConfig.Output}" />
                    </Grid>
                </StackPanel>
            </ui:CardExpander>

            <!--<ui:CardExpander Margin="0,0,0,12"
                             ContentPadding="0"
                             Icon="{ui:SymbolIcon Map24}"
                             IsExpanded="True">
                <ui:CardExpander.Header>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="地图追踪动作配置"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      TextWrapping="Wrap">
                            地图追踪到达目标点的时候会执行一些动作，有些动作必须配置对应角色在队伍内的位置
                        </ui:TextBlock>

                    </Grid>
                </ui:CardExpander.Header>
                <StackPanel>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="进行普通攻击的角色在队伍内的编号"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="一般用于收集需要元素反应才能采集的植物"
                                      TextWrapping="Wrap" />
                        <ComboBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="100"
                                  ItemsSource="{Binding PathingConfig.AvatarIndexList}"
                                  SelectedItem="{Binding PathingConfig.NormalAttackAvatarIndex}" />
                    </Grid>
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Row="0"
                                      Grid.Column="0"
                                      FontTypography="Body"
                                      Text="释放元素战技的角色在队伍内的编号"
                                      TextWrapping="Wrap" />
                        <ui:TextBlock Grid.Row="1"
                                      Grid.Column="0"
                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                      Text="用于采集石珀等"
                                      TextWrapping="Wrap" />
                        <ComboBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="100"
                                  ItemsSource="{Binding PathingConfig.AvatarIndexList}"
                                  SelectedItem="{Binding PathingConfig.ElementalSkillAvatarIndex}" />
                    </Grid>
                </StackPanel>
            </ui:CardExpander>-->
        </StackPanel>
    </ScrollViewer>
</UserControl>
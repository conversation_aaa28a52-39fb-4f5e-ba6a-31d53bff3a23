﻿<ui:FluentWindow x:Class="BetterGenshinImpact.View.Windows.WelcomeDialog"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 Width="500"
                 Height="210"
                 MinWidth="400"
                 MinHeight="230"
                 ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
                 ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                 ExtendsContentIntoTitleBar="True"
                 FontFamily="{DynamicResource TextThemeFontFamily}"
                 ResizeMode="CanMinimize"
                 WindowBackdropType="Mica"
                 WindowStartupLocation="CenterScreen"
                 WindowStyle="SingleBorderWindow"
                 xmlns:emoji="clr-namespace:Emoji.Wpf;assembly=Emoji.Wpf"
                 mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="1" Margin="12">
            <emoji:TextBlock Text="🔹 本软件开源且免费，喜欢的可以点🌟Star -> " Margin="5" FontSize="16">
                <Hyperlink NavigateUri="https://github.com/babalae/better-genshin-impact"
                           RequestNavigate="HyperlinkRequestNavigate" Foreground="#1E9BFA">
                    Github
                </Hyperlink>
            </emoji:TextBlock>
            <emoji:TextBlock Text="🔹 首次使用请先查看" Margin="5" FontSize="16">
                <Hyperlink NavigateUri="https://bettergi.com/quickstart.html"
                           RequestNavigate="HyperlinkRequestNavigate" Foreground="#1E9BFA">
                    《快速上手教程》
                </Hyperlink>
            </emoji:TextBlock>
            <emoji:TextBlock
                Margin="5"
                FontSize="16"
                TextWrapping="Wrap">
                <Run Text="❗本软件从未授权任何第三方平台进行" /><Run Text="售卖" Foreground="Red" />
                <Run Text="。如果你是付费获取的本软件（闲鱼、淘宝等），请举报商家并发起" /><Run Text="退款" Foreground="Red" /><Run Text="！" />
            </emoji:TextBlock>
            <!--<StackPanel Margin="5"
                        Orientation="Horizontal">
                <ui:TextBlock Text="本软件" VerticalAlignment="Bottom"/>
                <emoji:TextBlock Text="🔄开源" FontSize="20" />
                <ui:TextBlock Text="且" VerticalAlignment="Bottom"/>
                <emoji:TextBlock Text="🆓免费" FontSize="20"/>
            </StackPanel>-->

            <StackPanel Margin="5"
                        HorizontalAlignment="Right"
                        Orientation="Horizontal">
                <ui:Button Name="BtnOk"
                           Margin="5"
                           Appearance="Primary"
                           Click="BtnOkClick"
                           Content="确定"
                           IsDefault="True" />
            </StackPanel>
        </StackPanel>

        <ui:TitleBar Title="BetterGI" Grid.Row="0">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>

    </Grid>
</ui:FluentWindow>
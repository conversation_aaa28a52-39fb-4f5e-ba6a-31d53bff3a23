﻿<Page x:Class="BetterGenshinImpact.View.Pages.TaskSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
      xmlns:controls="clr-namespace:BetterGenshinImpact.View.Controls"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:helpers="clr-namespace:BetterGenshinImpact.Helpers"
      xmlns:markup="clr-namespace:BetterGenshinImpact.Markup"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      xmlns:emoji="clr-namespace:Emoji.Wpf;assembly=Emoji.Wpf"
      Title="TaskSettingsPage"
      d:DataContext="{d:DesignInstance Type=pages:TaskSettingsPageViewModel}"
      d:DesignHeight="1150"
      d:DesignWidth="750"
      ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
      ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      FontFamily="{StaticResource TextThemeFontFamily}"
      Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      mc:Ignorable="d">

    <StackPanel Margin="42,16,42,12">
        <ui:TextBlock Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="独立任务设置" />
        <!--  ~1~  停止任务  @1@  -->
        <!-- <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon DismissCircle24}"> -->
        <!--     <ui:CardControl.Header> -->
        <!--         <Grid> -->
        <!--             <Grid.RowDefinitions> -->
        <!--                 <RowDefinition Height="Auto" /> -->
        <!--                 <RowDefinition Height="Auto" /> -->
        <!--             </Grid.RowDefinitions> -->
        <!--  <ui:TextBlock Grid.Row="0"  -->
        <!--  Grid.Column="0"  -->
        <!--  FontTypography="Body"  -->
        <!--  Text="停止任务"  -->
        <!--  TextWrapping="Wrap" />  -->
        <!--  <ui:TextBlock Grid.Row="1"  -->
        <!--  Grid.Column="0"  -->
        <!--  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"  -->
        <!--  TextWrapping="Wrap">  -->
        <!--  停止下方独立任务的运行  -->
        <!--             </ui:TextBlock> -->
        <!--         </Grid> -->
        <!--     </ui:CardControl.Header> -->
        <!--  <ui:Button Margin="0,0,36,0"  -->
        <!--  Command="{Binding StopSoloTaskCommand}"  -->
        <!--  Content="停止任务"  -->
        <!--  Icon="{ui:SymbolIcon Dismiss24}" />  -->
        <!-- </ui:CardControl> -->


        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf6d2;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动七圣召唤"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        全自动打牌 -
                        <Hyperlink Command="{Binding GoToAutoGeniusInvokationUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoGeniusInvokationCommand}"
                                             EnableContent="{Binding SwitchAutoGeniusInvokationButtonText}"
                                             IsChecked="{Binding SwitchAutoGeniusInvokationEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择卡组"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="选择你想要使用的卡组与策略"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,12,0"
                               Content="脚本仓库"
                               Command="{Binding OpenLocalScriptRepoCommand}"
                               Icon="{ui:SymbolIcon Archive24}" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="2"
                              Width="180"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding AutoFightViewModel.StrategyList}"
                              SelectedItem="{Binding Config.AutoGeniusInvokationConfig.StrategyName, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="DropDownOpened">
                                <b:InvokeCommandAction Command="{Binding StrategyDropDownOpenedCommand}"
                                                       CommandParameter="GeniusInvocation" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ComboBox>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="设置延时（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="如果频繁出现操作速度过快，操作动画未播放完毕的情况可以添加延时"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  Margin="0,0,36,0"
                                  Maximum="5000"
                                  Minimum="0"
                                  ValidationMode="InvalidInputOverwritten"
                                  Value="{Binding Config.AutoGeniusInvokationConfig.SleepDelay, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf6b2;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动伐木"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        装备「王树瑞佑」，通过循环重启游戏刷新并收集木材 -
                        <Hyperlink Command="{Binding GoToAutoWoodUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoWoodCommand}"
                                             EnableContent="{Binding SwitchAutoWoodButtonText}"
                                             IsChecked="{Binding SwitchAutoWoodEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="循环次数"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="循环伐木多少次，输入 0 则为无限循环直到手动终止"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding AutoWoodRoundNum, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用OCR伐木数量限制（需1080P以上分辨率）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="伐木后OCR识别并累计木材数，达到上限后自动停止伐木"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoWoodConfig.WoodCountOcrEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="伐木数量上限（原神每日每种木材最多2000）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="启用伐木数量限制后生效，达到配置上限后自动停止伐木"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding AutoWoodDailyMaxCount, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="使用小道具后的额外延迟（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="如果希望看到使用小道具后获得木材的提示，可以调整这个值"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoWoodConfig.AfterZSleepDelay, Mode=TwoWay}" />
                </Grid>
                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="按下两次ESC（其中一下ESC是消除木材已满的提示）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="多木材点情况下，一种木材已满的时候使用这个功能"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoWoodConfig.PressTwoEscEnabled, Mode=TwoWay}" />
                </Grid>-->
            </StackPanel>
        </ui:CardExpander>

        <!--  自动战斗  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf71d;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动战斗"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        自动执行选择的战斗策略 -
                        <Hyperlink Command="{Binding GoToAutoFightUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoFightCommand}"
                                             EnableContent="{Binding SwitchAutoFightButtonText}"
                                             IsChecked="{Binding SwitchAutoFightEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择战斗策略"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="用于战斗"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,12,0"
                               Content="脚本仓库"
                               Command="{Binding OpenLocalScriptRepoCommand}"
                               Icon="{ui:SymbolIcon Archive24}" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="2"
                               Margin="0,0,12,0"
                               Command="{Binding OpenFightFolderCommand}"
                               Content="打开目录" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="3"
                              Width="180"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding AutoFightViewModel.CombatStrategyList}"
                              SelectedItem="{Binding Config.AutoFightConfig.StrategyName, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="DropDownOpened">
                                <b:InvokeCommandAction Command="{Binding StrategyDropDownOpenedCommand}"
                                                       CommandParameter="Combat" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ComboBox>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="根据技能CD优化出招人员"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="根据填入人或人和cd，来决定当此人元素战技cd未结束时，跳过此人出招，来优化战斗流程，可填入人名或人名数字（用逗号分隔），多种用分号分隔，例如:白术;钟离,12;，如果人名，则用内置cd检查（或填入数字也小于0），如果是人名和数字，则把数字当做出招cd(秒)。"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoFightConfig.ActionSchedulerByCd, Mode=TwoWay}"
                                TextWrapping="Wrap" />
                </Grid>
                <!--内嵌的设置-->
                <Separator Margin="-18,0" BorderThickness="0,1,0,0" />
                <Grid Margin="16,10,52,0">

                    <ui:CardExpander Margin="0,0,0,12"
                                     ContentPadding="0"
                                     IsExpanded="False">

                        <ui:CardExpander.Header>
                            <Grid>

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="自动检测战斗结束"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="检测到战斗已经结束的情况下，停止自动战斗功能"
                                              TextWrapping="Wrap" />
                                <ui:ToggleSwitch Grid.Row="0"
                                                 Grid.RowSpan="2"
                                                 Grid.Column="1"
                                                 Margin="0,0,36,0"
                                                 IsChecked="{Binding Config.AutoFightConfig.FightFinishDetectEnabled, Mode=TwoWay}" />
                            </Grid>
                        </ui:CardExpander.Header>
                        <StackPanel>
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Grid Margin="16,0,16,0">

                                    <ui:CardExpander Margin="0,0,0,12"
                                                     ContentPadding="0"
                                                     IsExpanded="False">

                                        <ui:CardExpander.Header>
                                            <Grid>

                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <ui:TextBlock Grid.Row="0"
                                                              Grid.Column="0"
                                                              FontTypography="Body"
                                                              Text="更快检查结束战斗"
                                                              TextWrapping="Wrap" />
                                                <ui:TextBlock Grid.Row="1"
                                                              Grid.Column="0"
                                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                              Text="快速检查战斗结束，在一轮脚本中，可以每隔一定秒数（默认为5）或指定角色操作后，去检查（在每个角色完成该轮脚本时）。"
                                                              TextWrapping="Wrap" />
                                                <ui:ToggleSwitch Grid.Row="0"
                                                                 Grid.RowSpan="2"
                                                                 Grid.Column="1"
                                                                 Margin="0,0,36,0"
                                                                 IsChecked="{Binding Config.AutoFightConfig.FinishDetectConfig.FastCheckEnabled, Mode=TwoWay}" />
                                            </Grid>
                                        </ui:CardExpander.Header>
                                        <StackPanel>
                                            <Grid Margin="16">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <ui:TextBlock Grid.Row="0"
                                                              Grid.Column="0"
                                                              FontTypography="Body"
                                                              Text="更快检查结束战斗参数"
                                                              TextWrapping="Wrap" />
                                                <ui:TextBlock Grid.Row="1"
                                                              Grid.Column="0"
                                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                              Text="快速检查战斗结束的参数，可填入数字和人名，多种用分号分隔，例如:5;白术;钟离;，如果是数字（小于等于0则不会根据时间去检查，单位为秒），则指定检查间隔，如果是人名，则该角色执行一轮操作后进行检查。同时每轮结束后检查不变。"
                                                              TextWrapping="Wrap" />
                                                <ui:TextBox Grid.Row="0"
                                                            Grid.RowSpan="2"
                                                            Grid.Column="1"
                                                            MinWidth="180"
                                                            MaxWidth="800"
                                                            Margin="0,0,36,0"
                                                            Text="{Binding Config.AutoFightConfig.FinishDetectConfig.FastCheckParams, Mode=TwoWay}"
                                                            TextWrapping="Wrap" />
                                            </Grid>
                                        </StackPanel>
                                    </ui:CardExpander>
                                </Grid>

                            </Grid>

                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="检查战斗结束的延时"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="检查战斗结束的延时，不同角色招式结束后的延时不一定相同，默认为1.5秒。也可以指定特定角色之后延时多少秒检查，未指定角色名，则默认为该值。格式如：2.5;白术,1.5;钟离,1.0;"
                                              TextWrapping="Wrap" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="1"
                                            MinWidth="120"
                                            Text="{Binding Config.AutoFightConfig.FinishDetectConfig.CheckEndDelay}" />
                            </Grid>
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="按键触发后检查延时"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="按下切换队伍后去检查屏幕色块的延时，默认为0.45秒。若频繁误判可以适当提高这个值，比如到0.75。确保这个延时不会真的把队伍配置界面切出来。"
                                              TextWrapping="Wrap" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="1"
                                            MinWidth="120"
                                            Text="{Binding Config.AutoFightConfig.FinishDetectConfig.BeforeDetectDelay}" />
                            </Grid>
                            <!--<Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="战斗结束基准色"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="判断战斗结束读条颜色，默认为95,235,255，一般无需修改"
                                              TextWrapping="Wrap" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="1"
                                            MinWidth="120"
                                            Text="{Binding Config.AutoFightConfig.FinishDetectConfig.BattleEndProgressBarColor}" />
                            </Grid>
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="战斗结束基准色偏差值"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="对于战斗结束基准色的偏差值，即±某个值（默认为6），例如 6或6,6,6，前者表示所有偏差值都一样，后者则可以分别设置"
                                              TextWrapping="Wrap" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="1"
                                            MinWidth="120"
                                            Text="{Binding Config.AutoFightConfig.FinishDetectConfig.BattleEndProgressBarColorTolerance}" />
                            </Grid>-->

                        </StackPanel>
                    </ui:CardExpander>
                </Grid>
                <Separator Margin="-18,0"
                           BorderThickness="0,1,0,0" />
                <Grid Margin="16,10,52,0">

                    <ui:CardExpander Margin="0,0,0,12"
                                     ContentPadding="0"
                                     IsExpanded="False">

                        <ui:CardExpander.Header>
                            <Grid>

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="自动拾取掉落物"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="战斗结束后尽可能拾取周围掉落物（与万叶配合更佳）"
                                              TextWrapping="Wrap" />
                                <ui:ToggleSwitch Grid.Row="0"
                                                 Grid.RowSpan="2"
                                                 Grid.Column="1"
                                                 Margin="0,0,36,0"
                                                 IsChecked="{Binding Config.AutoFightConfig.PickDropsAfterFightEnabled}" />
                            </Grid>
                        </ui:CardExpander.Header>
                        <StackPanel>
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBlock Grid.Row="0"
                                              Grid.Column="0"
                                              FontTypography="Body"
                                              Text="自动拾取掉落物时长"
                                              TextWrapping="Wrap" />
                                <ui:TextBlock Grid.Row="1"
                                              Grid.Column="0"
                                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                              Text="单位为秒。0表示不自动拾取掉落物。"
                                              TextWrapping="Wrap" />
                                <ui:TextBox Grid.Row="0"
                                            Grid.RowSpan="2"
                                            Grid.Column="1"
                                            Margin="0,0,36,0"
                                            MinWidth="120"
                                            Text="{Binding Config.AutoFightConfig.PickDropsAfterFightSeconds}" />
                            </Grid>
                        </StackPanel>
                    </ui:CardExpander>
                </Grid>
                <Separator Margin="-18,0" BorderThickness="0,1,0,0" />
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="战斗结束后使用万叶长E收集掉落物"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="队伍中存在枫原万叶的情况下，战斗结束后，使用长E拾取掉落物"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoFightConfig.KazuhaPickupEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="战斗超时（秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="当战斗超过一定时间后，自动停止战斗"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                Margin="0,0,36,0"
                                MinWidth="120"
                                Text="{Binding Config.AutoFightConfig.Timeout}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!--  自动刷本  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf438;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动秘境"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        基于钟离的自动循环刷本 -
                        <Hyperlink Command="{Binding GoToAutoDomainUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoDomainCommand}"
                                             EnableContent="{Binding SwitchAutoDomainButtonText}"
                                             IsChecked="{Binding SwitchAutoDomainEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择战斗策略"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="用于战斗"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,12,0"
                               Content="脚本仓库"
                               Command="{Binding OpenLocalScriptRepoCommand}"
                               Icon="{ui:SymbolIcon Archive24}" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="2"
                               Margin="0,0,12,0"
                               Command="{Binding OpenFightFolderCommand}"
                               Content="打开目录" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="3"
                              Width="180"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding AutoFightViewModel.CombatStrategyList}"
                              SelectedItem="{Binding Config.AutoFightConfig.StrategyName, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="DropDownOpened">
                                <b:InvokeCommandAction Command="{Binding StrategyDropDownOpenedCommand}"
                                                       CommandParameter="Combat" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ComboBox>
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动切换到指定队伍"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="注意队伍名称是游戏内你手动设置的名称"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoDomainConfig.PartyName, Mode=TwoWay}" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="指定要前往的秘境"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="自动传送到刷取的秘境"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="180"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding DomainNameList}"
                              SelectedItem="{Binding Config.AutoDomainConfig.DomainName, Mode=TwoWay}">
                    </ComboBox>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="刷取至树脂耗尽"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="优先使用浓缩树脂，然后使用原粹树脂，其余树脂不使用"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoDomainConfig.SpecifyResinUse,Converter={StaticResource InverseBooleanConverter}, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!-- 指定树脂使用开关 -->
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="指定每种树脂刷取次数"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="开启后会根据配置的次数使用对应的树脂"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoDomainConfig.SpecifyResinUse, Mode=TwoWay}" />
                </Grid>

                <!-- 树脂设置组 -->
                <Border Margin="16,8,16,16"
                        BorderThickness="1"
                        BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                        Background="{ui:ThemeResource ControlFillColorSecondaryBrush}"
                        CornerRadius="8"
                        IsEnabled="{Binding Config.AutoDomainConfig.SpecifyResinUse, Mode=OneWay}">
                    <StackPanel Margin="12">

                        <!-- 原粹树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,8,0,12">
                            <ui:TextBlock Text="原粹树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoDomainConfig.OriginalResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>

                        <!-- 浓缩树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,0,0,12">
                            <ui:TextBlock Text="浓缩树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoDomainConfig.CondensedResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>

                        <!-- 须臾树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,0,0,12">
                            <ui:TextBlock Text="须臾树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoDomainConfig.TransientResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>

                        <!-- 脆弱树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,0,0,0">
                            <ui:TextBlock Text="脆弱树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoDomainConfig.FragileResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>
                    </StackPanel>
                </Border>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="结束后自动分解圣遗物"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="需要快速分解圣遗物的最高星级"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="80"
                              Margin="0,0,10,0"
                              ItemsSource="{Binding Source={x:Static pages:TaskSettingsPageViewModel.ArtifactSalvageStarList}}"
                              SelectedItem="{Binding Config.AutoArtifactSalvageConfig.MaxArtifactStar, Mode=TwoWay}">
                    </ComboBox>
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="2"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoDomainConfig.AutoArtifactSalvage, Mode=TwoWay}" />

                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="战斗完成后等待时间（秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="战斗结束后，寻找石化古树前的延迟时间，等一些角色技能完全结束"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoDomainConfig.FightEndDelay, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="寻找古树时使用小步伐行走（正常用户请不要启用）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="如果电脑性能较差，寻找古树时间过久，可以尝试使用这个功能"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoDomainConfig.ShortMovement, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="步行前往开启秘境和领取奖励"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="如果电脑性能较差，开启秘境或者领取奖励的F点击不到，可以尝试此功能"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoDomainConfig.WalkToF, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="寻找古树时确认位置左右移动的次数（正常用户不要修改）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="小步伐行走的时候左右确认位置的次数"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoDomainConfig.LeftRightMoveTimes, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动吃药"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="请先装备 “便携营养袋” ，在红血时候后自动按Z键吃药"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoDomainConfig.AutoEat, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="角色死亡后重试次数"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="秘境战斗中，发生角色死亡重试的次数"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Text="{Binding Config.AutoDomainConfig.ReviveRetryCount, Mode=TwoWay}"
                                Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  自动幽境危战  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xe588;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动幽境危战"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        在接触钥匙后的界面启动本任务 -
                        <Hyperlink Command="{Binding GoToAutoStygianOnslaughtUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoStygianOnslaughtCommand}"
                                             EnableContent="{Binding SwitchAutoStygianOnslaughtButtonText}"
                                             IsChecked="{Binding SwitchAutoStygianOnslaughtEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择战斗策略"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="用于战斗"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,12,0"
                               Content="脚本仓库"
                               Command="{Binding OpenLocalScriptRepoCommand}"
                               Icon="{ui:SymbolIcon Archive24}" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="2"
                               Margin="0,0,12,0"
                               Command="{Binding OpenFightFolderCommand}"
                               Content="打开目录" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="3"
                              Width="180"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding AutoFightViewModel.CombatStrategyList}"
                              SelectedItem="{Binding Config.AutoStygianOnslaughtConfig.StrategyName, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="DropDownOpened">
                                <b:InvokeCommandAction Command="{Binding StrategyDropDownOpenedCommand}"
                                                       CommandParameter="Combat" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ComboBox>
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="刷取至树脂耗尽"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="优先使用浓缩树脂，然后使用原粹树脂，其余树脂不使用"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoStygianOnslaughtConfig.SpecifyResinUse,Converter={StaticResource InverseBooleanConverter}, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!-- 指定树脂使用开关 -->
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="指定每种树脂刷取次数"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="开启后会根据配置的次数使用对应的树脂"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoStygianOnslaughtConfig.SpecifyResinUse, Mode=TwoWay}" />
                </Grid>

                <!-- 树脂设置组 -->
                <Border Margin="16,8,16,16"
                        BorderThickness="1"
                        BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                        Background="{ui:ThemeResource ControlFillColorSecondaryBrush}"
                        CornerRadius="8"
                        IsEnabled="{Binding Config.AutoStygianOnslaughtConfig.SpecifyResinUse, Mode=OneWay}">
                    <StackPanel Margin="12">

                        <!-- 原粹树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,8,0,12">
                            <ui:TextBlock Text="原粹树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoStygianOnslaughtConfig.OriginalResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>

                        <!-- 浓缩树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,0,0,12">
                            <ui:TextBlock Text="浓缩树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoStygianOnslaughtConfig.CondensedResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>

                        <!-- 须臾树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,0,0,12">
                            <ui:TextBlock Text="须臾树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoStygianOnslaughtConfig.TransientResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>

                        <!-- 脆弱树脂设置 -->
                        <StackPanel Orientation="Horizontal"
                                    Margin="0,0,0,0">
                            <ui:TextBlock Text="脆弱树脂刷取次数："
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0" />
                            <ui:NumberBox
                                Value="{Binding Config.AutoStygianOnslaughtConfig.FragileResinUseCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                Minimum="0"
                                SmallChange="1"
                                LargeChange="5"
                                SpinButtonPlacementMode="Inline"
                                Width="120" />
                        </StackPanel>
                    </StackPanel>
                </Border>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="结束后自动分解圣遗物"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="需要快速分解圣遗物的最高星级"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="80"
                              Margin="0,0,10,0"
                              ItemsSource="{Binding Source={x:Static pages:TaskSettingsPageViewModel.ArtifactSalvageStarList}}"
                              SelectedItem="{Binding Config.AutoArtifactSalvageConfig.MaxArtifactStar, Mode=TwoWay}">
                    </ComboBox>
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="2"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoStygianOnslaughtConfig.AutoArtifactSalvage, Mode=TwoWay}" />

                </Grid>

            </StackPanel>
        </ui:CardExpander>

        <!--<ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Visibility="{markup:Converter Value={x:Static helpers:RuntimeHelper.IsDebuggerAttached},
                                                       Converter={StaticResource BooleanToVisibilityConverter}}">

            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf54b;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>

            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动跟踪"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        自动走路跟踪剧情任务，不支持每日任务 -
                        <Hyperlink Command="{Binding GoToAutoTrackUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,24,0"
                               Command="{Binding SwitchAutoTrackCommand}"
                               Content="{Binding SwitchAutoTrackButtonText}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="强制指定队伍"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="队伍无法被识别时，请按顺序填写队伍内角色名称，逗号分割"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoFightConfig.TeamNames, Mode=TwoWay}"
                                TextWrapping="Wrap" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>-->

        <!--  自动音游专辑  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0"
                         Icon="{ui:SymbolIcon MusicNote224}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动千音雅集"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        可以自动演奏单个，也可以全自动完成整个专辑 -
                        <Hyperlink Command="{Binding GoToAutoMusicGameUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                </Grid>
            </ui:CardExpander.Header>

            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="【乐曲】 演奏单个乐曲"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        进入演奏界面使用，下落模式必须选择垂落模式 -
                        <Hyperlink Command="{Binding GoToAutoMusicGameUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,36,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoMusicGameCommand}"
                                             EnableContent="{Binding SwitchAutoMusicGameButtonText}"
                                             IsChecked="{Binding SwitchAutoMusicGameEnabled}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="【专辑】 全自动完成整个专辑"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        进入专辑界面使用，自动演奏未完成乐曲 -
                        <Hyperlink Command="{Binding GoToAutoMusicGameUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,36,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoAlbumCommand}"
                                             EnableContent="{Binding SwitchAutoAlbumButtonText}"
                                             IsChecked="{Binding SwitchAutoAlbumEnabled}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="【专辑】 自动演奏未达成【大音天籁】的乐曲"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="关闭时，奖励已经领取就会跳过乐曲。开启时，达成了【大音天籁】才会跳过乐曲"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoMusicGameConfig.MustCanorusLevel, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="【专辑】 自动演奏的目标难度选择"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="设置为【传说】，【大师】即可获取所有奖励，设置【所有】则会对乐曲的所有难度进行自动演奏"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="80"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding AutoMusicLevelList}"
                              SelectedItem="{Binding Config.AutoMusicGameConfig.MusicLevel, Mode=TwoWay}"
                              SelectedIndex="0" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- 自动钓鱼 -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xe3a8;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="全自动钓鱼（单个鱼塘）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        不要携带跟宠！在出现钓鱼F按钮的位置启动本任务 -
                        <Hyperlink Command="{Binding GoToAutoFishingUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoFishingCommand}"
                                             EnableContent="{Binding SwitchAutoFishingButtonText}"
                                             IsChecked="{Binding SwitchAutoFishingEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择需要钓的鱼"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="选择需要钓的鱼，如果不选择则默认为所有鱼"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="80"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding AutoMusicLevelList}"
                              SelectedItem="{Binding Config.AutoFishingConfig.XXXXX, Mode=TwoWay}" 
                              SelectedIndex="0" />
                </Grid>-->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="上钩等待超时时间"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="超过这个时间将自动提竿，并重新识别并选择鱼饵进行抛竿"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  Margin="0,0,36,0"
                                  Maximum="60"
                                  Minimum="5"
                                  ValidationMode="InvalidInputOverwritten"
                                  Value="{Binding Config.AutoFishingConfig.AutoThrowRodTimeOut, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                  Text="{Binding Config.AutoFishingConfig.AutoThrowRodTimeOut, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="整个任务超时时间"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="超过这个时间将强制结束任务"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  Margin="0,0,36,0"
                                  Maximum="1800"
                                  Minimum="0"
                                  ValidationMode="InvalidInputOverwritten"
                                  Value="{Binding Config.AutoFishingConfig.WholeProcessTimeoutSeconds, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                  Text="{Binding Config.AutoFishingConfig.WholeProcessTimeoutSeconds, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="昼夜策略"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="钓全天的鱼、还是只钓白天或夜晚的鱼、亦或不调整时间"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="100"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding FishingTimePolicyDict}"
                              SelectedValuePath="Key"
                              DisplayMemberPath="Value"
                              SelectedItem="{Binding Config.AutoFishingConfig.FishingTimePolicy, Converter={StaticResource EnumToKVPConverter}, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="关键帧保存截图（开发者）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="在流程判断的关键时刻保存当时的截图，供分析判断。会大量写入，非调试时请关闭。需要启用保存截图功能"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsEnabled="{Binding Config.CommonConfig.ScreenshotEnabled}"
                                     IsChecked="{Binding SaveScreenshotOnKeyTick, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="torch库文件地址（仅限2.5.1版本）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        请
                        <Hyperlink Command="{Binding GoToTorchPreviousVersionsCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            下载
                        </Hyperlink>
                        到本地后填入torch_cpu.dll或torch_cuda.dll的完整地址。如未生效可尝试重启BGI。

                    </ui:TextBlock>
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoFishingConfig.TorchDllFullPath, Mode=TwoWay}"
                                TextWrapping="Wrap" Cursor="IBeam" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- 自动使用兑换码 -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0" Icon="{ui:SymbolIcon BarcodeScanner24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动使用兑换码"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        自动使用输入的兑换码
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchAutoRedeemCodeCommand}"
                                             EnableContent="{Binding SwitchAutoRedeemCodeButtonText}"
                                             IsChecked="{Binding SwitchAutoRedeemCodeEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="上钩等待超时时间"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="超过这个时间将自动提竿，并重新识别并选择鱼饵进行抛竿"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  Margin="0,0,36,0"
                                  Maximum="60"
                                  Minimum="5"
                                  ValidationMode="InvalidInputOverwritten"
                                  Value="{Binding Config.AutoFishingConfig.AutoThrowRodTimeOut, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                  Text="{Binding Config.AutoFishingConfig.AutoThrowRodTimeOut, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="整个任务超时时间"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="超过这个时间将强制结束任务"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  Margin="0,0,36,0"
                                  Maximum="1800"
                                  Minimum="0"
                                  ValidationMode="InvalidInputOverwritten"
                                  Value="{Binding Config.AutoFishingConfig.WholeProcessTimeoutSeconds, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                  Text="{Binding Config.AutoFishingConfig.WholeProcessTimeoutSeconds, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="昼夜策略"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="钓全天的鱼、还是只钓白天或夜晚的鱼、亦或不调整时间"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="100"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding FishingTimePolicyDict}"
                              SelectedValuePath="Key"
                              DisplayMemberPath="Value"
                              SelectedItem="{Binding Config.AutoFishingConfig.FishingTimePolicy, Converter={StaticResource EnumToKVPConverter}, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="关键帧保存截图（开发者）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="在流程判断的关键时刻保存当时的截图，供分析判断。会大量写入，非调试时请关闭。需要启用保存截图功能"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsEnabled="{Binding Config.CommonConfig.ScreenshotEnabled}"
                                     IsChecked="{Binding SaveScreenshotOnKeyTick, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="torch库文件地址（仅限2.5.1版本）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        请
                        <Hyperlink Command="{Binding GoToTorchPreviousVersionsCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            下载
                        </Hyperlink>
                        到本地后填入torch_cpu.dll或torch_cuda.dll的完整地址。如未生效可尝试重启BGI。

                    </ui:TextBlock>
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoFishingConfig.TorchDllFullPath, Mode=TwoWay}"
                                TextWrapping="Wrap" Cursor="IBeam" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- 自动分解圣遗物 -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf4bb;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动分解圣遗物"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        指定匹配表达式逐一筛选分解，支持5星圣遗物
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchArtifactSalvageCommand}"
                                             EnableContent="{Binding SwitchAutoFishingButtonText}"
                                             IsChecked="{Binding SwitchArtifactSalvageEnabled}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="测试识别效果"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="请先将游戏界面切换至圣遗物分解界面"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Content="打开测试窗口"
                               Command="{Binding OpenArtifactSalvageTestOCRWindowCommand}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="3*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="正则表达式"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="只要满足的圣遗物都会被选中"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="180"
                                MaxWidth="800"
                                Margin="0,0,36,0"
                                Text="{Binding Config.AutoArtifactSalvageConfig.RegularExpression, Mode=TwoWay}"
                                TextWrapping="Wrap" Cursor="IBeam" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="需要快速分解圣遗物的最高星级"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="先会进行一次快速分解选择"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="80"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding Source={x:Static pages:TaskSettingsPageViewModel.ArtifactSalvageStarList}}"
                              SelectedItem="{Binding Config.AutoArtifactSalvageConfig.MaxArtifactStar, Mode=TwoWay}">
                    </ComboBox>
                </Grid>
                <Grid Margin="16" Cursor="">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="7*" />
                        <ColumnDefinition Width="3*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="最大检查数量"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="达到最大检查数量后也会停止"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="90"
                                  Margin="0,0,36,0"
                                  Value="{Binding Config.AutoArtifactSalvageConfig.MaxNumToCheck, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                  Text="{Binding Config.AutoArtifactSalvageConfig.MaxNumToCheck, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!-- 截取物品图标（开发者） -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0"
                         Visibility="{Binding Config.CommonConfig.ScreenshotEnabled, Converter={StaticResource BooleanToVisibilityConverter}}">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf4bb;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="183*" />
                        <ColumnDefinition Width="70*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="截取物品图标（开发者）"
                                  TextWrapping="Wrap" Grid.ColumnSpan="2" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Grid.RowSpan="2"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap" Grid.ColumnSpan="2">
                        须要打开设置-启用保存截图功能，文件保存在
                        <Hyperlink Command="{Binding GoToGridIconsFolderCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            log/gridIcons
                        </Hyperlink>
                        <LineBreak />
                        以下过长的内容在pr时会搬到教程里去
                        <LineBreak />
                        需要漆黑的背景以降低干扰，比如渊下宫-蛇肠之路的一个锚点，将视角竖直向上看向洞顶
                        <LineBreak />
                        诸如提纳里的耳朵太长了，他装备的物品角标目前无法正确地和正上方的物品图标进行轮廓分割，请手动规避
                    </ui:TextBlock>
                    <controls:TwoStateButton Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="2"
                                             Margin="0,0,24,0"
                                             DisableCommand="{Binding StopSoloTaskCommand}"
                                             DisableContent="停止"
                                             EnableCommand="{Binding SwitchGetGridIconsCommand}"
                                             EnableContent="{Binding SwitchGetGridIconsButtonText}"
                                             IsChecked="{Binding SwitchGetGridIconsEnabled}" VerticalAlignment="Top" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="界面名称"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="不同界面的参数不一样，请选择你要扫描的界面"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="150"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding GridNameDict}"
                              SelectedValuePath="Key"
                              DisplayMemberPath="Value"
                              SelectedItem="{Binding Config.GetGridIconsConfig.GridName, Converter={StaticResource EnumToKVPConverter}, Mode=TwoWay}">
                    </ComboBox>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="使用星星作为名称后缀（待开发）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="有些物品具有相同的名称，但具有不同的图标和星星数"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsEnabled="{Binding Config.GetGridIconsConfig.StarAsSuffix, Mode=TwoWay}"
                                     IsChecked="{Binding Config.GetGridIconsConfig.StarAsSuffix, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="使用等级作为名称后缀（待开发）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="有些物品具有相同的名称，但具有不同的图标和等级"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsEnabled="{Binding Config.GetGridIconsConfig.LvAsSuffix, Mode=TwoWay}"
                                     IsChecked="{Binding Config.GetGridIconsConfig.LvAsSuffix, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16" Cursor="">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="7*" />
                        <ColumnDefinition Width="3*" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="最大截取数量"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="达到最大截取数量后会停止"
                                  TextWrapping="Wrap" />
                    <ui:NumberBox Grid.Row="0"
                                  Grid.RowSpan="2"
                                  Grid.Column="1"
                                  MinWidth="90"
                                  Margin="0,0,36,0"
                                  Value="{Binding Config.GetGridIconsConfig.MaxNumToGet, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                  Text="{Binding Config.GetGridIconsConfig.MaxNumToGet, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--<ui:CardExpander Margin="0,0,0,12" ContentPadding="0" Icon="{ui:SymbolIcon Accessibility24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="自动前进" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="自动朝着前方前进，自动飞行、游泳、爬山" />
                    <ui:Button
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        Margin="0,0,24,0"
                        Content="绑定快捷键"
                        Command="{Binding GoToHotKeyPageCommand}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="夜兰自动 E" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="【开发中..】" />
                    <ui:ToggleSwitch
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        Margin="0,0,36,0"
                        IsChecked="{Binding Config.AutoFishingConfig.AutoThrowRodEnabled, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>-->
        <!--<ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon PlayingCards20}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="自动战斗" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="按下快捷键自动开始战斗" />
                </Grid>
            </ui:CardControl.Header>
            <ui:Button
                Margin="0,0,36,0"
                Content="绑定快捷键"
                Command="{Binding GoToHotKeyPageCommand}" />
        </ui:CardControl>-->
    </StackPanel>
</Page>
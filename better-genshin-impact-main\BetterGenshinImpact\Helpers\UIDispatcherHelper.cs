﻿using System;
using System.Windows;

namespace BetterGenshinImpact.Helpers;

public static class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>H<PERSON>per
{
    public static Window MainWindow => Application.Current.Dispatcher.Invoke(() => Application.Current.MainWindow) ?? throw new InvalidOperationException();

    public static void Invoke(Action callback, params object[] args)
    {
        _ = Application.Current.Dispatcher.Invoke(callback, args);
    }

    public static void Invoke(Action<Window> callback)
    {
        _ = Application.Current.Dispatcher.Invoke(callback, MainWindow);
    }

    public static T Invoke<T>(Func<T> func)
        where T : class
    {
        return Application.Current.Dispatcher.Invoke(func);
    }

    public static void BeginInvoke(Action callback, params object[] args)
    {
        _ = Application.Current.Dispatcher.BeginInvoke(callback, args);
    }

    public static void BeginInvoke(Action<Window> callback)
    {
        _ = Application.Current.Dispatcher.BeginInvoke(callback, MainWindow);
    }
}
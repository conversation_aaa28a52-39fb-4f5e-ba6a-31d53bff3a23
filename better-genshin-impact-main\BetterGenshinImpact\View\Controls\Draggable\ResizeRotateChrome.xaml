﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:adorners="clr-namespace:BetterGenshinImpact.View.Controls.Adorners"
                    xmlns:controls="clr-namespace:BetterGenshinImpact.View.Controls">

    <Style TargetType="{x:Type Shape}" x:Key="ThumbCorner">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Stroke" Value="DeepSkyBlue" />
        <Setter Property="StrokeThickness" Value=".5" />
        <Setter Property="Width" Value="7" />
        <Setter Property="Height" Value="7" />
        <Setter Property="Margin" Value="-2" />
        <Setter Property="Fill" Value="DeepSkyBlue" />
            <!--<Setter Property="Fill">
            <Setter.Value>
                <RadialGradientBrush Center="0.2, 0.2" GradientOrigin="0.2, 0.2" RadiusX="0.8" RadiusY="0.8">
                    <GradientStop Color="White" Offset="0.0" />
                    <GradientStop Color="Gray" Offset="0.8" />
                </RadialGradientBrush>
            </Setter.Value>
        </Setter>-->
    </Style>

    <Style TargetType="{x:Type adorners:ResizeRotateChrome}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type adorners:ResizeRotateChrome}">
                    <Grid>
                        <Grid Opacity="0" Margin="-3">
                            <!--<s:RotateThumb Width="7"
                                           Height="7"
                                           Margin="0,-20,0,0"
                                           Cursor="Hand"
                                           VerticalAlignment="Top"
                                           HorizontalAlignment="Center" />-->
                            <controls:ResizeThumb Height="3"
                                           Cursor="SizeNS"
                                           VerticalAlignment="Top"
                                           HorizontalAlignment="Stretch" />
                            <controls:ResizeThumb Width="3"
                                           Cursor="SizeWE"
                                           VerticalAlignment="Stretch"
                                           HorizontalAlignment="Left" />
                            <controls:ResizeThumb Width="3"
                                           Cursor="SizeWE"
                                           VerticalAlignment="Stretch"
                                           HorizontalAlignment="Right" />
                            <controls:ResizeThumb Height="3"
                                           Cursor="SizeNS"
                                           VerticalAlignment="Bottom"
                                           HorizontalAlignment="Stretch" />
                            <controls:ResizeThumb Width="7"
                                           Height="7"
                                           Margin="-2"
                                           Cursor="SizeNWSE"
                                           VerticalAlignment="Top"
                                           HorizontalAlignment="Left" />
                            <controls:ResizeThumb Width="7"
                                           Height="7"
                                           Margin="-2"
                                           Cursor="SizeNESW"
                                           VerticalAlignment="Top"
                                           HorizontalAlignment="Right" />
                            <controls:ResizeThumb Width="7"
                                           Height="7"
                                           Margin="-2"
                                           Cursor="SizeNESW"
                                           VerticalAlignment="Bottom"
                                           HorizontalAlignment="Left" />
                            <controls:ResizeThumb Width="7"
                                           Height="7"
                                           Margin="-2"
                                           Cursor="SizeNWSE"
                                           VerticalAlignment="Bottom"
                                           HorizontalAlignment="Right" />
                        </Grid>
                        <Grid IsHitTestVisible="False" Opacity="1" Margin="-3">
                            <Rectangle SnapsToDevicePixels="True"
                                       StrokeThickness="1"
                                       StrokeDashArray="5 5"
                                       Margin="1"
                                       Stroke="SkyBlue" />
                            <!--<Line StrokeThickness="1" X1="0" Y1="0" X2="0" Y2="20"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Top"
                                  Margin="0,-19,0,0"
                                  Stroke="Blue" />
                            <Ellipse Style="{StaticResource ThumbCorner}" HorizontalAlignment="Center"
                                     VerticalAlignment="Top" Margin="-1,-20,0,0" />-->
                            <Rectangle Style="{StaticResource ThumbCorner}" HorizontalAlignment="Left"
                                       VerticalAlignment="Top" />
                            <Rectangle Style="{StaticResource ThumbCorner}" HorizontalAlignment="Right"
                                       VerticalAlignment="Top" />
                            <Rectangle Style="{StaticResource ThumbCorner}" HorizontalAlignment="Left"
                                       VerticalAlignment="Bottom" />
                            <Rectangle Style="{StaticResource ThumbCorner}" HorizontalAlignment="Right"
                                       VerticalAlignment="Bottom" />
                        </Grid>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
﻿<Window x:Class="BetterGenshinImpact.View.PickerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:view="clr-namespace:BetterGenshinImpact.View"
        Title="选择捕获窗口"
        Width="800"
        Height="450"
        WindowStartupLocation="CenterScreen"
        Background="#FF1E1E1E">

    <Window.Resources>
        <!-- ListBoxItem Fluent UI 样式 -->
        <Style TargetType="ListBoxItem">
            <Setter Property="Margin" Value="8,4" />
            <Setter Property="Padding" Value="12" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border x:Name="Border" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="1"
                                BorderBrush="Transparent">
                            <Grid>
                                <Border x:Name="SelectionBackground"
                                        Background="#30FFFFFF"
                                        CornerRadius="8"
                                        Opacity="0"/>
                                <ContentPresenter />
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="SelectionBackground" Property="Opacity" Value="0.1" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="#20FFFFFF" />
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="SelectionBackground" Property="Opacity" Value="0.2" />
                                <Setter TargetName="Border" Property="BorderBrush" Value="#40FFFFFF" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" 
                Background="#FF252525"
                Padding="24,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Text="选择捕获窗口" 
                         FontSize="24" 
                         FontWeight="SemiBold"
                         Foreground="#FFFFFF"/>

                <TextBlock Grid.Row="1"
                         Text="双击选中要捕获的窗口" 
                         Margin="0,8,0,0"
                         Foreground="#99FFFFFF"
                         FontSize="14"/>
            </Grid>
        </Border>

        <ScrollViewer Grid.Row="1"
              PanningMode="VerticalOnly"
              ScrollViewer.VerticalScrollBarVisibility="Auto"
              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
            <ListBox x:Name="WindowList"
             Background="Transparent"
             BorderThickness="0"
             MouseDoubleClick="WindowsOnMouseDoubleClick"
             VirtualizingPanel.IsVirtualizing="True"
             VirtualizingPanel.VirtualizationMode="Recycling"
             ScrollViewer.CanContentScroll="False">
                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem">
                        <Setter Property="Padding" Value="0"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ListBoxItem">
                                    <Border x:Name="Border" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="1"
                                BorderBrush="Transparent">
                                        <ContentPresenter/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Trigger.EnterActions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <ColorAnimation Storyboard.TargetName="Border"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="#22FFFFFF"
                                                          Duration="0:0:0.2">
                                                            <ColorAnimation.EasingFunction>
                                                                <ExponentialEase EasingMode="EaseOut"/>
                                                            </ColorAnimation.EasingFunction>
                                                        </ColorAnimation>
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </Trigger.EnterActions>
                                            <Trigger.ExitActions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <ColorAnimation Storyboard.TargetName="Border"
                                                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                          To="#00000000"
                                                          Duration="0:0:0.2">
                                                            <ColorAnimation.EasingFunction>
                                                                <ExponentialEase EasingMode="EaseOut"/>
                                                            </ColorAnimation.EasingFunction>
                                                        </ColorAnimation>
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </Trigger.ExitActions>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#33FFFFFF"/>
                                            <Setter TargetName="Border" Property="BorderBrush" Value="#44FFFFFF"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </ListBox.ItemContainerStyle>

                <ListBox.ItemTemplate>
                    <DataTemplate DataType="{x:Type view:PickerWindow+CapturableWindow}">
                        <Grid Margin="12,8" Height="48">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 内容层 -->
                            <Border Grid.Column="0" 
                        Width="40" 
                        Height="40"
                        Background="#22FFFFFF"
                        CornerRadius="4"
                        Margin="0,0,12,0">
                                <Image Width="24" 
                           Height="24" 
                           Source="{Binding Icon}"
                           RenderOptions.BitmapScalingMode="HighQuality"/>
                            </Border>

                            <StackPanel Grid.Column="1" 
                            VerticalAlignment="Center">
                                <TextBlock Text="{Binding Name}" 
                             FontSize="14" 
                             FontWeight="SemiBold"
                             Foreground="#FFFFFF"/>
                                <TextBlock Text="{Binding ProcessName}" 
                             FontSize="12" 
                             Foreground="#99FFFFFF"
                             Margin="0,4,0,0"/>
                            </StackPanel>
                        </Grid>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </ScrollViewer>
    </Grid>
</Window>

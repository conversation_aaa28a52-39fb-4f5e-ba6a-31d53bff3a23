﻿using System.Threading;
using System.Threading.Tasks;
using BetterGenshinImpact.GameTask.AutoPathing.Model;
using BetterGenshinImpact.GameTask.Common.Job;

namespace BetterGenshinImpact.GameTask.AutoPathing.Handler;

public class ExitAndReloginHandler : IActionHandler
{
    private readonly ExitAndReloginJob _exitAndReloginJob = new();

    public async Task RunAsync(CancellationToken ct, WaypointForTrack? waypointForTrack = null, object? config = null)
    {
        await _exitAndReloginJob.Start(ct);
    }
}
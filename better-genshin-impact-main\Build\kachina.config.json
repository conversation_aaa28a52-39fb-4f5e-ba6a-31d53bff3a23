{"source": [{"id": "dfs", "name": "默认服务器", "uri": "dfs+packed+https://steambird.cocogoat.cn/dfs/bgi/BetterGI.Install.exe"}, {"id": "mirrorc", "name": "Mirror酱", "uri": "mirrorc://BGI?os=win&arch=x64"}, {"id": "mirrorc-alpha", "name": "Mirror酱 Alpha", "uri": "mirrorc://BGI?channel=alpha&os=win&arch=x64", "hidden": true}], "appName": "BetterGI", "publisher": "baba<PERSON>e", "regName": "BetterGI", "exeName": "BetterGI.exe", "uninstallName": "BetterGI.uninst.exe", "updaterName": "BetterGI.update.exe", "programFilesPath": "BetterGI", "title": "BetterGI", "description": "更好的原神，免费且开源", "windowTitle": "BetterGI 安装程序", "userDataPath": ["${INSTALL_PATH}/User"], "extraUninstallPath": ["${INSTALL_PATH}/log"], "uacStrategy": "prefer-admin", "runtimes": ["Microsoft.DotNet.DesktopRuntime.8", "Microsoft.VCRedist.2015+.x64"]}
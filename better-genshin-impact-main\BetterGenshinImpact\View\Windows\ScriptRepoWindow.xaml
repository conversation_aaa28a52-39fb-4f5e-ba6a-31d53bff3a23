<ui:FluentWindow x:Class="BetterGenshinImpact.View.Windows.ScriptRepoWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:local="clr-namespace:BetterGenshinImpact.View.Windows"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 xmlns:vio="http://schemas.lepo.co/wpfui/2022/xaml/violeta"
                 Title="脚本仓库"
                 Width="350"
                 MinWidth="350"
                 MinHeight="50"
                 Background="#202020"
                 ExtendsContentIntoTitleBar="True"
                 FontFamily="{DynamicResource TextThemeFontFamily}"
                 SizeToContent="Height"
                 WindowBackdropType="None"
                 WindowStartupLocation="CenterOwner"
                 mc:Ignorable="d">
    <Grid>
        <ui:Grid Margin="0,48,0,0">
            <Border Grid.Column="2"
                    Margin="6"
                    Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1,1,1,1"
                    CornerRadius="8">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  第一行：更新渠道  -->
                    <StackPanel Grid.Row="0"
                                Margin="0,0,0,12"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                        <ui:TextBlock Margin="0,0,8,0"
                                      VerticalAlignment="Center"
                                      Foreground="{ui:ThemeResource TextFillColorPrimaryBrush}"
                                      Text="更新渠道：" />
                        <ComboBox Width="160"
                                  VerticalAlignment="Center"
                                  DisplayMemberPath="Name"
                                  ItemsSource="{Binding RepoChannels}"
                                  SelectedItem="{Binding SelectedRepoChannel}" />


                    </StackPanel>
                    <!--  第二行：脚本仓库URL  -->
                    <StackPanel Grid.Row="1"
                                Margin="0,0,0,12"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                        <ui:TextBlock Margin="0,0,8,0"
                                      VerticalAlignment="Center"
                                      Foreground="{ui:ThemeResource TextFillColorPrimaryBrush}"
                                      Text="仓库地址：" />
                        <TextBox MinWidth="160"
                                 MaxWidth="200"
                                 VerticalAlignment="Center"
                                 IsEnabled="{Binding IsRepoUrlReadOnly, Converter={StaticResource InverseBooleanConverter}}"
                                 IsReadOnly="{Binding IsRepoUrlReadOnly}"
                                 Text="{Binding Config.SelectedRepoUrl}" />
                    </StackPanel>

                    <!--  第三行：脚本仓库  -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal">
                        <ui:Button Margin="0,0,8,0"
                                   Command="{Binding UpdateRepoCommand}"
                                   Content="更新仓库"
                                   Icon="{ui:SymbolIcon CloudSync24}" />
                        <ui:Button Margin="0,0,8,0"
                                   Command="{Binding ResetRepoCommand}"
                                   Content="重置仓库"
                                   Icon="{ui:SymbolIcon ArrowReset24}" />
                        <ui:Button Appearance="Primary"
                                   Command="{Binding OpenLocalScriptRepoCommand}"
                                   Content="打开仓库"
                                   Icon="{ui:SymbolIcon BookStar24}" />
                    </StackPanel>

                    <StackPanel Grid.Row="3"
                                Margin="0,8,0,4"
                                Visibility="{Binding IsUpdating, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ui:TextBlock Margin="0,0,8,0"
                                      VerticalAlignment="Center"
                                      Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}"
                                      Text="{Binding UpdateProgressText}" />
                        <ProgressBar Maximum="100" Value="{Binding UpdateProgressValue}" />
                    </StackPanel>
                </Grid>
            </Border>
        </ui:Grid>

        <ui:TitleBar Title="{Binding Title}"
                     ShowMaximize="False"
                     ShowMinimize="False">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>
    </Grid>
</ui:FluentWindow>
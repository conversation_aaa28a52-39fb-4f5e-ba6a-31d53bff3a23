﻿<UserControl x:Class="BetterGenshinImpact.View.Pages.HotKeyPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:controls="clr-namespace:BetterGenshinImpact.View.Controls"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hotKey="clr-namespace:BetterGenshinImpact.View.Controls.HotKey"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:model="clr-namespace:BetterGenshinImpact.Model"
             xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             d:DataContext="{d:DesignInstance Type=pages:HotKeyPageViewModel}"
             d:DesignHeight="850"
             d:DesignWidth="600"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             FontFamily="{StaticResource TextThemeFontFamily}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             mc:Ignorable="d">

    <Grid Margin="42,16,42,12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <ui:TextBlock Grid.Row="0"
                      Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="快捷键设置" />
        <ui:TextBlock Grid.Row="1"
                      Margin="0,0,0,8"
                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                      Text="全局热键：只支持组合键和功能键，软件启动直接生效。键鼠监听：支持任意键盘单键、鼠标侧键，功能启动后才生效（推荐）。点击类型按钮可以切换快捷键类型。其中存在长按需求的功能不能使用全局热键。"
                      TextWrapping="Wrap" />

        <!--<ItemsControl Grid.Row="2" ItemsSource="{Binding HotKeySettingModels}">
            <ItemsControl.ItemTemplate>
                <DataTemplate DataType="{x:Type model:HotKeySettingModel}">
                    <Grid Margin="0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="100" />
                            <ColumnDefinition Width="180" />
                        </Grid.ColumnDefinitions>
                        <ui:TextBlock Grid.Column="0"
                                      VerticalAlignment="Center"
                                      Text="{Binding FunctionName}" />
                        <ui:Button Grid.Column="1"
                                   Margin="0,0,8,0"
                                   HorizontalAlignment="Right"
                                   Command="{Binding SwitchHotKeyTypeCommand}"
                                   Content="{Binding HotKeyTypeName, Mode=OneWay}"
                                   IsEnabled="{Binding SwitchHotkeyTypeEnabled, Mode=OneWay}" />
                        <hotKey:HotKeyTextBox Grid.Column="2"
                                              HotKeyTypeName="{Binding HotKeyTypeName, Mode=OneWay}"
                                              Hotkey="{Binding HotKey}"
                                              Style="{StaticResource DefaultTextBoxStyle}"
                                              TextAlignment="Center" />
                    </Grid>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>-->

        <ui:Border Grid.Row="2"
                   Background="{DynamicResource CardBackground}"
                   BorderBrush="{DynamicResource CardBorderBrush}"
                   BorderThickness="1"
                   CornerRadius="{DynamicResource ControlCornerRadius}">
            <Grid Margin="4">
                <ui:Grid ColumnDefinitions="*,235" Visibility="Hidden">
                    <Grid x:Name="TreeColumnStar" Grid.Column="0" />
                </ui:Grid>
                <ui:TreeListView BorderThickness="0" ItemsSource="{Binding HotKeySettingModels}">
                    <ui:TreeListView.Columns>
                        <GridViewColumnCollection>
                            <ui:GridViewColumn Width="{Binding ActualWidth, ElementName=TreeColumnStar}" Header="功能">
                                <ui:GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <ui:TreeRowExpander Content="{Binding FunctionName}" />
                                    </DataTemplate>
                                </ui:GridViewColumn.CellTemplate>
                            </ui:GridViewColumn>
                            <ui:GridViewColumn Width="100" Header="快捷键类型">
                                <ui:GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <ui:Button Height="33"
                                                   VerticalAlignment="Center"
                                                   Command="{Binding SwitchHotKeyTypeCommand}"
                                                   Content="{Binding HotKeyTypeName, Mode=OneWay}"
                                                   IsEnabled="{Binding SwitchHotkeyTypeEnabled, Mode=OneWay}"
                                                   Visibility="{Binding IsDirectory, Converter={StaticResource BooleanToVisibilityRevertConverter}, Mode=OneWay}" />
                                    </DataTemplate>
                                </ui:GridViewColumn.CellTemplate>
                            </ui:GridViewColumn>
                            <ui:GridViewColumn Width="120" Header="配置快捷键">
                                <ui:GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <hotKey:HotKeyTextBox Padding="16,6,16,6"
                                                              VerticalAlignment="Center"
                                                              HotKeyTypeName="{Binding HotKeyTypeName, Mode=OneWay}"
                                                              Hotkey="{Binding HotKey}"
                                                              Style="{StaticResource DefaultTextBoxStyle}"
                                                              TextAlignment="Center"
                                                              Visibility="{Binding IsDirectory, Converter={StaticResource BooleanToVisibilityRevertConverter}, Mode=OneWay}" />
                                    </DataTemplate>
                                </ui:GridViewColumn.CellTemplate>
                            </ui:GridViewColumn>
                        </GridViewColumnCollection>
                    </ui:TreeListView.Columns>
                    <ui:TreeListView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding Children}" />
                    </ui:TreeListView.ItemTemplate>
                </ui:TreeListView>
            </Grid>
        </ui:Border>
    </Grid>
</UserControl>
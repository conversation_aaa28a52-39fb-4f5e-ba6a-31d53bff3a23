﻿<Window x:Class="BetterGenshinImpact.Test.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        Title="Test"
        Width="240"
        Height="450"
        mc:Ignorable="d">
    <Canvas>
        <Button Canvas.Left="10"
                Canvas.Top="10"
                Width="100"
                Height="23"
                Click="ShowCameraRecWindow"
                Content="视角识别测试" />
        <Button Canvas.Left="10"
                Canvas.Top="40"
                Width="100"
                Height="23"
                Click="ShowHsvTestWindow"
                Content="Hsv拉条" />
        <Button Canvas.Left="10"
                Canvas.Top="70"
                Width="100"
                Height="23"
                Click="DoMapPuzzle"
                Content="拼接超大地图" />
        <Button Canvas.Left="10"
                Canvas.Top="100"
                Width="100"
                Height="23"
                Click="DoMatchTest"
                Content="生成特征点" />
        <Button Canvas.Left="10"
                Canvas.Top="130"
                Width="100"
                Height="23"
                Click="DoMatchTemplateTest"
                Content="模板匹配测试" />
        <Button Canvas.Left="10"
                Canvas.Top="160"
                Width="100"
                Height="23"
                Click="MapDrawTeleportPoint"
                Content="画传送点" />
        <Button Canvas.Left="10"
                Canvas.Top="190"
                Width="100"
                Height="23"
                Click="GenAvatarData"
                Content="侧面头像数据集" />
        <Button Canvas.Left="120"
                Canvas.Top="190"
                Width="100"
                Height="23"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Click="GenAvatarDataT"
                Content="透明头像数据集" />
        <Button Canvas.Left="10"
                Canvas.Top="220"
                Width="100"
                Height="23"
                Click="AutoCookTestCase"
                Content="自动烹饪图像测试" />
        <Button Canvas.Left="10"
                Canvas.Top="250"
                Width="100"
                Height="23"
                Click="MapPathView"
                Content="路线地图查看" />
        <Button Canvas.Left="120"
                Canvas.Top="10"
                Width="100"
                Height="23"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Click="ZoomOut"
                Content="缩小地图到256" />
        <Button Canvas.Left="120"
                Canvas.Top="40"
                Width="100"
                Height="23"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Click="CameraTest"
                Content="新视角识别测试" />
    </Canvas>
</Window>
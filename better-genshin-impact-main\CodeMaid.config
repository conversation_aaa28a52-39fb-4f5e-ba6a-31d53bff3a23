<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="SteveCadwallader.CodeMaid.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <SteveCadwallader.CodeMaid.Properties.Settings>
            <setting name="General_Theme" serializeAs="String">
                <value>1</value>
            </setting>
            <setting name="Cleaning_AutoCleanupOnFileSave" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="ThirdParty_UseXAMLStylerCleanup" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_RemoveEndOfFileTrailingNewLine" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="Cleaning_IncludeCPlusPlus" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeXAML" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeXML" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludePHP" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeCSS" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeHTML" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeTypeScript" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeJSON" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeJavaScript" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeSCSS" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeLESS" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludeR" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_IncludePowerShell" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_InsertEndOfFileTrailingNewLine" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="Cleaning_InsertExplicitAccessModifiersOnClasses"
                serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_InsertExplicitAccessModifiersOnDelegates"
                serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_InsertExplicitAccessModifiersOnEnumerations"
                serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_InsertExplicitAccessModifiersOnInterfaces"
                serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_InsertExplicitAccessModifiersOnStructs"
                serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Cleaning_InsertExplicitAccessModifiersOnMethods"
                serializeAs="String">
                <value>False</value>
            </setting>
        </SteveCadwallader.CodeMaid.Properties.Settings>
    </userSettings>
</configuration>
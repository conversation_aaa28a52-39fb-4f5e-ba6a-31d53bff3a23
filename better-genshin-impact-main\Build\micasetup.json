{"Template": "${MicaDir}/template/default.7z", "Package": "./publish.7z", "Output": "./${AppName}_Setup.exe", "AppName": "BetterGI", "KeyName": "BetterGI", "ExeName": "BetterGI.exe", "Publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TargetFramework": "net472", "Guid": "00000000-0000-0000-0000-000000000000", "Favicon": "./img/Favicon.png", "Icon": "./img/FaviconSetup.png", "UnIcon": "./img/FaviconUninst.png", "LicenseFile": "LICENSE", "License": null, "LicenseType": null, "RequestExecutionLevel": "admin", "SingleInstanceMutex": null, "IsCreateDesktopShortcut": true, "IsCreateUninst": true, "IsCreateStartMenu": true, "IsPinToStartMenu": false, "IsCreateQuickLaunch": false, "IsCreateRegistryKeys": true, "IsCreateAsAutoRun": false, "IsCustomizeVisiableAutoRun": false, "AutoRunLaunchCommand": "-autostart", "IsUseFolderPickerPreferClassic": false, "IsUseInstallPathPreferX86": false, "IsUseRegistryPreferX86": null, "IsAllowFullFolderSecurity": true, "IsAllowFirewall": true, "IsRefreshExplorer": true, "IsInstallCertificate": false, "IsEnableUninstallDelayUntilReboot": true, "IsEnvironmentVariable": false, "OverlayInstallRemoveExt": "exe,dll,pdb", "UnpackingPassword": null, "MessageOfPage1": null, "MessageOfPage2": null, "MessageOfPage3": null}
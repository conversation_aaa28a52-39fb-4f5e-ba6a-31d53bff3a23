﻿<div align="center">
  <h1 align="center">
    <a href="https://bettergi.com/"><img src="https://img.alicdn.com/imgextra/i2/2042484851/O1CN014wn1rf1lhoFYjL0gA_!!2042484851.png" width="200"></a>
    <br/>
    <a href="https://bettergi.com/">BetterGI</a>
  </h1>
  <a href="https://trendshift.io/repositories/5269" target="_blank"><img src="https://trendshift.io/api/badge/repositories/5269" alt="babalae%2Fbetter-genshin-impact | Trendshift" style="width: 200px; height: 46px;" width="250" height="46"/></a>
</div>

<br/>

<div align="center">
  <a href="https://dotnet.microsoft.com/zh-cn/download/dotnet/latest/runtime"><img alt="Windows" src="https://img.shields.io/badge/platform-Windows-blue?logo=windowsxp&style=flat-square&color=1E9BFA" /></a>
  <a href="https://github.com/babalae/better-genshin-impact/releases"><img alt="Downloads" src="https://img.shields.io/github/downloads/babalae/better-genshin-impact/total?logo=github&style=flat-square&color=1E9BFA"></a>
  <a href="https://github.com/babalae/better-genshin-impact/releases"><img alt="Release" src="https://img.shields.io/github/v/release/babalae/better-genshin-impact?logo=visualstudio&style=flat-square&color=1E9BFA"></a>
</div>

<br/>

<div align="center">
🌟 Click the Star in the top-right corner to get notified about updates on GitHub!
</div>

<div align="center">
    <img src="https://img.alicdn.com/imgextra/i1/2042484851/O1CN01OL1E1v1lhoM7Wdmup_!!2042484851.gif" alt="Star" width="186" height="60">
  </a>
</div>

<br/>  

[English](./readme_en.md) | [中文](../README.md)| [繁体中文](./readme_tcn.md)

[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-%237289DA?style=for-the-badge&logo=discord&logoColor=white)](https://discord.gg/8xUfcw5nTS)

BetterGI · A Better Genshin Impact experience, powered by computer vision technology.

## Features
* **Real-Time Tasks**
    * **[Auto Pickup](https://bettergi.com/feats/timer/pick.html):** Automatically press <kbd>F</kbd> for interactions/pickups. Supports whitelist/blacklist configuration.
    * **[Auto Dialogue/Skip](https://bettergi.com/feats/timer/skip.html):** Fast-click through dialogues, auto-select options, auto-submit items, close popups, etc.
        * Automatically claims Daily Commission rewards **[and re-dispatches expeditions](https://bettergi.com/feats/timer/skip.html#%E8%87%AA%E5%8A%A8%E9%87%8D%E6%96%B0%E6%B4%BE%E9%81%A3)** when talking to Katherine.
    * **[Auto Hangout](https://bettergi.com/feats/timer/skip.html#%E8%87%AA%E5%8A%A8%E9%82%80%E7%BA%A6):** Automatically selects Hangout options (requires Auto Dialogue enabled).
    * **[Quick Teleport](https://bettergi.com/feats/timer/tp.html):** Auto-clicks map teleport points and initiates teleportation.
    * **[Semi-Auto Fishing](https://bettergi.com/feats/timer/fish.html):** AI-based auto-casting, auto-hook detection, and auto-catch mechanics.
    * **[Auto Cooking](https://bettergi.com/feats/timer/cook.html):** Perfectly cooks dishes (excluding "Bountiful Year" recipes).

* **Standalone Tasks**
    * **[Auto Genius Invokation TCG](https://bettergi.com/feats/task/tcg.html):** Automates PvE TCG challenges like character invites and weekly duels.
    * **[Auto Woodcutting](https://bettergi.com/feats/task/felling.html):** Uses "Woodland Encounter" (<kbd>Z</kbd>) and relogs to farm wood efficiently.
    * **[Auto Domain Runs](https://bettergi.com/feats/task/domain.html):** Fully automated domain clears, including starting, combat, and claiming rewards.
    * **[Auto Rhythm Game](https://bettergi.com/feats/task/music.html):** Completes "Rhythm of the Realm" albums for achievements.
    * **[Full Auto Fishing](https://bettergi.com/feats/task/fish.html):** Fully automates fishing at designated spots, including day/night transitions.

* **Full Automation**
    * **[One-Click Daily Routine](https://github.com/babalae/better-genshin-impact/issues/846):** Completes daily tasks (using Adventure EXP) and claims rewards.
    * **[Auto Gather/Mine/Farm](https://bettergi.com/feats/autos/pathing.html):** Auto-collect resources via minimap detection.
    * **[Input Macro Recording](https://bettergi.com/feats/autos/kmscript.html):** Records and replays keyboard/mouse actions (works with scheduler).

* **Quality-of-Life**
    * **[Neuvillette Spin](https://bettergi.com/feats/macro/other.html#%E9%82%A3%E7%BB%B4%E8%8E%B1%E7%89%B9-%E8%BD%AC%E5%9C%88%E5%9C%88):** Hold a key to spin the camera horizontally (also works for Nahida).
    * **[Quick Artifact Enhancement](https://bettergi.com/feats/macro/other.html#%E5%9C%A3%E9%81%97%E7%89%A9%E4%B8%80%E9%94%AE%E5%BC%BA%E5%8C%96):** Skip animation by switching between "Details" and "Enhance" tabs.
    * **[Shop Quick Buy](https://bettergi.com/feats/macro/other.html#%E4%B8%80%E9%94%AE%E8%B4%AD%E4%B9%B0):** Instantly buy max quantities of shop items (events/Serenitea Pot).
* **[...and more](https://bettergi.com/doc.html)**

<div align="center">
  <img src="https://github.com/babalae/better-genshin-impact/assets/15783049/57ab7c3c-709a-4cf3-8f64-1c78764c364c"/>
  <p>Includes an overlay showing logs and computer vision processing results.</p>
</div>

## Screenshots

![0 39 1](https://github.com/user-attachments/assets/8fb0bfd9-e0db-4289-800f-1bc2efb221aa)

## Download

> [!NOTE]
> Download: [⚡GitHub Releases](https://github.com/babalae/better-genshin-impact/releases)
>
> New user? See: [Quick Start](https://bettergi.com/quickstart.html). Issues? Check [FAQ](https://bettergi.com/faq.html).

Latest builds: [![](https://github.com/babalae/better-genshin-impact/actions/workflows/publish.yml/badge.svg)](https://github.com/babalae/better-genshin-impact/actions/workflows/publish.yml)

## Usage
**Requirements:**
- Windows 10/11 (64-bit)
- [.NET 8 Runtime](https://dotnet.microsoft.com/download/dotnet/8.0) (auto-prompted if missing)

**⚠️ Notes:**
1. Restart the app after changing window size/resolution/monitor.
2. Disable all visual filters (HDR/NVIDIA Filters). Keep in-game brightness default.
3. Only supports `16:9` resolutions (recommended: `1920x1080` windowed).
4. **Antivirus/security software may block simulated inputs. Add to whitelist.**

Start the app, select a capture method on the "Launch" page, then click "Start"!

Detailed guide: [Quick Start](https://bettergi.com/quickstart.html)

Full documentation: [Documentation](https://bettergi.com/doc.html)

## FAQ
* **Why admin rights?**
    - The game runs as admin. Simulated clicks require matching permissions.
* **Ban risk?**
    - **No game files/memory are modified.** Only visual detection and simulated inputs. However, miHoYo's Terms of Service prohibit third-party tools. Use at your own discretion.
* [More FAQs...](https://bettergi.com/faq.html)

## Credits
Special thanks to these projects:
* [Yap](https://github.com/Alex-Beng/Yap)
* [genshin-woodmen](https://github.com/genshin-matrix/genshin-woodmen)
* [Fischless](https://github.com/genshin-matrix/Fischless)
* [MicaSetup](https://github.com/lemutec/MicaSetup)
* [cvAutoTrack](https://github.com/GengGode/cvAutoTrack)
* [genshin_impact_assistant](https://github.com/infstellar/genshin_impact_assistant)
* [HutaoFisher](https://github.com/myHuTao-qwq/HutaoFisher)
* [minimap](https://github.com/tignioj/minimap)
* [kachina-installer](https://github.com/YuehaiTeam/kachina-installer)

Core contributors: [@Lightczx](https://github.com/Lightczx), [@emako](https://github.com/emako)

## Development
Formatting: [CodeMaid.config](CodeMaid.config), [Settings.XamlStyler](Settings.XamlStyler)

[Build instructions](BetterGenshinImpact/README.md)

## License
![GPL-v3](https://www.gnu.org/graphics/gplv3-127x51.png)

## Support
Report issues: [GitHub Issues](https://github.com/babalae/better-genshin-impact/issues)  

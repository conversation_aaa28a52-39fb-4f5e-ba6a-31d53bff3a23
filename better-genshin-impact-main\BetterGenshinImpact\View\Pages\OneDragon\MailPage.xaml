﻿<UserControl x:Class="BetterGenshinImpact.View.Pages.OneDragon.MailPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:oneDragon="clr-namespace:BetterGenshinImpact.ViewModel.Pages.OneDragon"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             d:DataContext="{d:DesignInstance Type=oneDragon:MailViewModel}"
             d:DesignHeight="450"
             d:DesignWidth="800"
             mc:Ignorable="d">
    <StackPanel>
        <TextBlock Margin="0,0,0,10"
                   FontSize="15"
                   FontWeight="Bold"
                   Text="{Binding Title}" />

        <ui:ToggleSwitch Margin="0,0,0,10"
                         Content="领取所有邮件"
                         IsChecked="{Binding CollectAllMail}" />

        <ui:ToggleSwitch Margin="0,0,0,10"
                         Content="领取附件"
                         IsChecked="{Binding CollectAttachments}" />

        <ui:NumberBox Margin="0,0,0,10"
                      Maximum="100"
                      Minimum="1"
                      PlaceholderText="最大领取邮件数量"
                      Value="{Binding MaxMailsToCollect}" />
    </StackPanel>
</UserControl>

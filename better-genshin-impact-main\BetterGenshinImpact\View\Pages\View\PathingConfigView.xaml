﻿<ui:FluentWindow x:Class="BetterGenshinImpact.View.Pages.View.PathingConfigView"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:dd="urn:gong-wpf-dragdrop"
                 xmlns:local="clr-namespace:BetterGenshinImpact.Model"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:sdl="http://schemas.sdl.com/xaml"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 xmlns:view="clr-namespace:BetterGenshinImpact.ViewModel.Pages.View"
                 Width="1000"
                 Height="500"
                 ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
                 ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                 d:DataContext="{d:DesignInstance Type=view:PathingConfigViewModel}"
                 d:DesignHeight="1000"
                 d:DesignWidth="500"
                 mc:Ignorable="d">

    <b:Interaction.Triggers>
        <b:EventTrigger EventName="Closing">
            <b:InvokeCommandAction Command="{Binding ClosingCommand}" PassEventArgsToCommand="True" />
        </b:EventTrigger>
    </b:Interaction.Triggers>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <ScrollViewer Grid.Row="1">
            <StackPanel Margin="12,0">

                <StackPanel Margin="10,5">
                    <ui:TextBlock Margin="0,0,0,8"
                                  FontSize="16"
                                  FontTypography="BodyStrong"
                                  Text="地图追踪设置（相关配置优先级从高到低,UI样式还有问题未解决）" />

                </StackPanel>

                <ui:Card Margin="0,0,0,10">
                    <StackPanel>

                        <ui:TextBlock Margin="0,0,0,8"
                                      FontTypography="BodyStrong"
                                      Text="队伍切换设置（路径中存在符合条件的动作、采集物的情况下，自动切换至对应队伍）" />
                        <!--  使用ItemsControl来动态显示筛选条件  -->
                        <ItemsControl dd:DragDrop.IsDragSource="True"
                                      dd:DragDrop.IsDropTarget="True"
                                      dd:DragDrop.UseDefaultDragAdorner="True"
                                      ItemsSource="{Binding Config.PathingConditionConfig.PartyConditions, Mode=TwoWay}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <WrapPanel Margin="0,0,0,10">
                                        <ui:FontIcon Margin="0,0,15,0"
                                                     FontSize="20"
                                                     Glyph="&#xe411;"
                                                     Style="{StaticResource FaFontIconStyleForOneDragon}" />
                                        <!--  采集物、路径动作  -->
                                        <ComboBox MinWidth="100"
                                                  Margin="0,0,10,5"
                                                  HorizontalAlignment="Left"
                                                  ItemsSource="{Binding Source={x:Static local:ConditionDefinitions.PartySubjects}}"
                                                  SelectedItem="{Binding Subject, Mode=TwoWay}" />

                                        <!--  包含  -->
                                        <ComboBox MinWidth="80"
                                                  Margin="0,0,10,5"
                                                  HorizontalAlignment="Left"
                                                  ItemsSource="{Binding Definition.PredicateOptions}"
                                                  SelectedItem="{Binding Predicate, Mode=TwoWay}" />

                                        <!--  搜索输入框  -->
                                        <sdl:MultiSelectComboBox Width="300"
                                                                 MinWidth="300"
                                                                 Background="{DynamicResource ApplicationBackgroundBrush}"
                                                                 EnableAutoComplete="True"
                                                                 EnableBatchSelection="True"
                                                                 EnableFiltering="True"
                                                                 Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                                 IsEditable="True"
                                                                 ItemsSource="{Binding Definition.ObjectOptions, Mode=TwoWay}"
                                                                 MaxDropDownHeight="300"
                                                                 SelectedItems="{Binding Object, Mode=TwoWay}"
                                                                 SelectionMode="Multiple">
                                            <!--<sdl:MultiSelectComboBox.Resources>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}" />
                                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
                                            </Style>
                                            <SolidColorBrush x:Key="MultiSelectComboBox.Text.Foreground" Color="#ffffff" />
                                        </sdl:MultiSelectComboBox.Resources>-->
                                        </sdl:MultiSelectComboBox>

                                        <ui:TextBlock Margin="10,0"
                                                      VerticalAlignment="Center"
                                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}">
                                            则切换到队伍
                                        </ui:TextBlock>


                                        <!--  结果  -->
                                        <ui:TextBox MinWidth="160"
                                                    Margin="0,0,10,0"
                                                    Text="{Binding Result, Mode=TwoWay}" />

                                        <!--  清除按钮  -->
                                        <Button
                                            Command="{Binding DataContext.RemovePartyConditionConfigCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                            CommandParameter="{Binding}"
                                            Content="删除" />
                                    </WrapPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>

                        <!--  添加条件按钮  -->
                        <Button HorizontalAlignment="Left"
                                Command="{Binding AddPartyConditionConfigCommand}"
                                Content="+ 添加条件" />
                    </StackPanel>
                </ui:Card>

                <ui:Card Margin="0,0,0,10">
                    <StackPanel>

                        <ui:TextBlock Margin="0,0,0,8"
                                      FontTypography="BodyStrong"
                                      Text="角色设置（存在多个符合条件的角色情况下，优先使用游戏内编队编号更小的）" />
                        <!--  使用ItemsControl来动态显示筛选条件  -->
                        <ItemsControl dd:DragDrop.IsDragSource="True"
                                      dd:DragDrop.IsDropTarget="True"
                                      dd:DragDrop.UseDefaultDragAdorner="True"
                                      ItemsSource="{Binding Config.PathingConditionConfig.AvatarConditions, Mode=TwoWay}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <WrapPanel Margin="0,0,0,10">
                                        <ui:FontIcon Margin="0,0,15,0"
                                                     FontSize="20"
                                                     Glyph="&#xe411;"
                                                     Style="{StaticResource FaFontIconStyleForOneDragon}" />
                                        <!--  角色  -->
                                        <ComboBox MinWidth="100"
                                                  Margin="0,0,10,5"
                                                  HorizontalAlignment="Left"
                                                  ItemsSource="{Binding Source={x:Static local:ConditionDefinitions.AvatarSubjects}}"
                                                  SelectedItem="{Binding Subject, Mode=TwoWay}" />

                                        <!--  包含  -->
                                        <ComboBox MinWidth="80"
                                                  Margin="0,0,10,5"
                                                  HorizontalAlignment="Left"
                                                  ItemsSource="{Binding Definition.PredicateOptions}"
                                                  SelectedItem="{Binding Predicate}" />

                                        <!--  搜索输入框  -->
                                        <Grid Width="300" MinWidth="300">
                                            <sdl:MultiSelectComboBox
                                                Background="{DynamicResource ApplicationBackgroundBrush}"
                                                EnableAutoComplete="True"
                                                EnableBatchSelection="True"
                                                EnableFiltering="True"
                                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                IsEditable="True"
                                                ItemsSource="{Binding Definition.ObjectOptions, Mode=TwoWay}"
                                                MaxDropDownHeight="300"
                                                SelectedItems="{Binding Object, Mode=TwoWay}"
                                                SelectionMode="Multiple" />

                                        </Grid>

                                        <ui:TextBlock Margin="10,0"
                                                      VerticalAlignment="Center"
                                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}">
                                            则角色执行
                                        </ui:TextBlock>

                                        <!--  结果  -->
                                        <ComboBox MinWidth="160"
                                                  Margin="0,0,10,0"
                                                  HorizontalAlignment="Left"
                                                  ItemsSource="{Binding Source={x:Static local:ConditionDefinitions.AvatarResultList}}"
                                                  SelectedItem="{Binding Result}" />

                                        <!--  清除按钮  -->
                                        <Button
                                            Command="{Binding DataContext.RemoveAvatarConditionConfigCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                            CommandParameter="{Binding}"
                                            Content="删除" />
                                    </WrapPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>

                        <!--  添加条件按钮  -->
                        <Button HorizontalAlignment="Left"
                                Command="{Binding AddAvatarConditionConfigCommand}"
                                Content="+ 添加条件" />
                    </StackPanel>
                </ui:Card>



                <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon BoxToolbox24}">

                    <ui:CardControl.Header>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="使用小道具的间隔时间（毫秒）"
                                          TextWrapping="Wrap" />
                            <ui:TextBlock Grid.Row="1"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="在移动期间自动使用小道具，填0为不使用"
                                          TextWrapping="Wrap" />
                        </Grid>
                    </ui:CardControl.Header>

                    <ui:TextBox
                        MinWidth="100"
                        Margin="0,0,36,0"
                        Text="{Binding Config.PathingConditionConfig.UseGadgetIntervalMs, Mode=TwoWay}"
                        TextWrapping="NoWrap" />
                </ui:CardControl>
                
                
                <ui:CardControl Margin="0,0,0,12" >
                    <ui:CardControl.Icon>
                        <ui:FontIcon Glyph="&#xe3b2;" Style="{StaticResource FaFontIconStyle}" />
                    </ui:CardControl.Icon>
                    <ui:CardControl.Header>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="只在传送点时恢复"
                                          TextWrapping="Wrap" />
                            <ui:TextBlock Grid.Row="1"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="只有路径在传送点时，才会去七天神像恢复"
                                          TextWrapping="Wrap" />
                        </Grid>
                    </ui:CardControl.Header>

                    <ui:ToggleSwitch
                        Margin="0,0,36,0"
                        IsChecked="{Binding Config.PathingConditionConfig.OnlyInTeleportRecover, Mode=TwoWay}" />
                </ui:CardControl>
            </StackPanel>
        </ScrollViewer>

        <ui:TitleBar Name="MyTitleBar" Grid.Row="0">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>

    </Grid>


</ui:FluentWindow>
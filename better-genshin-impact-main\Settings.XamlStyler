{"AttributesTolerance": 2, "KeepFirstAttributeOnSameLine": true, "MaxAttributeCharactersPerLine": 0, "MaxAttributesPerLine": 1, "NewlineExemptionElements": "RadialGradientBrush, GradientStop, LinearGradientBrush, ScaleTransform, SkewTransform, RotateTransform, TranslateTransform, Trigger, Condition, Setter", "SeparateByGroups": false, "AttributeIndentation": 0, "AttributeIndentationStyle": 1, "RemoveDesignTimeReferences": false, "IgnoreDesignTimeReferencePrefix": false, "EnableAttributeReordering": true, "AttributeOrderingRuleGroups": ["x:Class", "xmlns, xmlns:x", "xmlns:*", "x:Key, Key, x:Name, Name, x:Uid, Uid, Title", "G<PERSON><PERSON>Row, <PERSON><PERSON>.<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.<PERSON><PERSON><PERSON>, Canvas.Left, Canvas.Top, Canvas.Right, Canvas.Bottom", "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "Margin, Padding, HorizontalAlignment, VerticalAlignment, HorizontalContentAlignment, VerticalContentAlignment, Panel.ZIndex", "*:*, *", "PageSource, PageIndex, Offset, Color, TargetName, Property, Value, StartPoint, EndPoint", "mc:I<PERSON>rable, d:IsDataSource, d:LayoutOverrides, d:IsStaticText", "Storyboard.*, From, To, Duration"], "FirstLineAttributes": "", "OrderAttributesByName": true, "PutEndingBracketOnNewLine": false, "RemoveEndingTagOfEmptyElement": true, "SpaceBeforeClosingSlash": true, "RootElementLineBreakRule": 0, "ReorderVSM": 2, "ReorderGridChildren": false, "ReorderCanvasChildren": false, "ReorderSetters": 0, "FormatMarkupExtension": true, "NoNewLineMarkupExtensions": "x:Bind, Binding", "ThicknessSeparator": 2, "ThicknessAttributes": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ThumbnailClipMargin", "FormatOnSave": true, "CommentPadding": 2}
﻿<ui:FluentWindow x:Class="BetterGenshinImpact.View.Windows.OcrDialog"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 SizeToContent="WidthAndHeight"
                 ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
                 ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                 ExtendsContentIntoTitleBar="True"
                 FontFamily="{DynamicResource TextThemeFontFamily}"
                 ResizeMode="CanMinimize"
                 WindowBackdropType="Mica"
                 WindowStartupLocation="CenterScreen"
                 WindowStyle="SingleBorderWindow"
                 mc:Ignorable="d">
    <StackPanel>
        <ui:TitleBar Name="MyTitleBar">
            <ui:TitleBar.Icon>
                <ui:ImageIcon Source="pack://application:,,,/Resources/Images/logo.png" />
            </ui:TitleBar.Icon>
        </ui:TitleBar>
        <Grid>
            <Border BorderThickness="3">
                <Border.BorderBrush>
                    <VisualBrush>
                        <VisualBrush.Visual>
                            <Rectangle StrokeDashArray="3 5" Stroke="#86ffff" StrokeThickness="3"
                                                         Width="{Binding RelativeSource={RelativeSource AncestorType={x:Type Border}}, Path=ActualWidth}"
                                                         Height="{Binding RelativeSource={RelativeSource AncestorType={x:Type Border}}, Path=ActualHeight}"  />
                        </VisualBrush.Visual>
                    </VisualBrush>
                </Border.BorderBrush>
            </Border>
            <Image Name="Screenshot" Source="pack://application:,,,/Resources/Images/logo.png" Stretch="None" Margin="3"/>
        </Grid>
        <StackPanel Margin="12">
            <ui:TextBox Name="TxtRecognized"  Margin="5" />
            <ui:TextBox Name="RegexResult" Margin="5" />
            <StackPanel Margin="5"
                        HorizontalAlignment="Right"
                        Orientation="Horizontal">
                <ui:Button Name="BtnOk"
                           Margin="5"
                           Appearance="Primary"
                           Click="BtnOkClick"
                           Content="刷新"
                           IsDefault="True" />
                <ui:Button Name="BtnCancel"
                           Margin="5"
                           Click="BtnCancelClick"
                           Content="退出"
                           IsCancel="True" />
            </StackPanel>
        </StackPanel>

    </StackPanel>
</ui:FluentWindow>

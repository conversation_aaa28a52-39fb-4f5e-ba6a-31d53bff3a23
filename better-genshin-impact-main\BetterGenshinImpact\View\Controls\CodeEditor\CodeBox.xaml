﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ava="clr-namespace:ICSharpCode.AvalonEdit;assembly=ICSharpCode.AvalonEdit"
                    xmlns:local="clr-namespace:BetterGenshinImpact.View.Controls"
                    xmlns:codeEditor="clr-namespace:BetterGenshinImpact.View.Controls.CodeEditor">

    <Style x:Key="DefaultTextEditorStyle" TargetType="{x:Type ava:TextEditor}">
        <Setter Property="FontFamily" Value="{DynamicResource TextThemeFontFamily}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="White" />
    </Style>

    <Style BasedOn="{StaticResource DefaultTextEditorStyle}" TargetType="{x:Type ava:TextEditor}" />
    <Style BasedOn="{StaticResource DefaultTextEditorStyle}" TargetType="{x:Type codeEditor:CodeBox}" />
    <Style BasedOn="{StaticResource DefaultTextEditorStyle}" TargetType="{x:Type codeEditor:JsonCodeBox}" />

</ResourceDictionary>

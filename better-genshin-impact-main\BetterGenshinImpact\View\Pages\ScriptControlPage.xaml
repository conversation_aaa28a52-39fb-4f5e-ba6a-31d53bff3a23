﻿<UserControl x:Class="BetterGenshinImpact.View.Pages.ScriptControlPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dd="urn:gong-wpf-dragdrop"
             xmlns:local="clr-namespace:BetterGenshinImpact.View.Pages"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             d:DataContext="{d:DesignInstance Type=pages:ScriptControlViewModel}"
             d:DesignHeight="850"
             d:DesignWidth="800"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             FontFamily="{StaticResource TextThemeFontFamily}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/View/Controls/Style/ListViewEx.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Margin="8,0,8,0">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="150" />
            <ColumnDefinition Width="16" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!--  左侧栏  -->
        <Grid Grid.Column="0" Margin="0,8,8,8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Border Grid.Row="0"
                        BorderBrush="{DynamicResource ControlStrongFillColorDisabledBrush}"
                        BorderThickness="0,0,0,1">
                    <ui:TextBlock Margin="4,4,8,4"
                                  FontTypography="BodyStrong"
                                  Text="配置组"
                                  TextAlignment="Center" />
                </Border>
                <ui:ListView Grid.Row="1"
                             dd:DragDrop.IsDragSource="True"
                             dd:DragDrop.IsDropTarget="True"
                             dd:DragDrop.UseDefaultDragAdorner="True"
                             ItemsSource="{Binding ScriptGroups, Mode=TwoWay}"
                             SelectedItem="{Binding SelectedScriptGroup, Mode=TwoWay}"
                             SelectionMode="Single">
                    <ui:ListView.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Margin="8,4" Text="{Binding Name, Mode=OneWay}" >
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="White" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding NextFlag}" Value="True">
                                                <Setter Property="Foreground" Value="Green" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                              </TextBlock>
                        </DataTemplate>
                    </ui:ListView.ItemTemplate>
                    <ui:ListView.ContextMenu>
                        <ContextMenu>
                            <MenuItem Command="{Binding AddScriptGroupCommand}" Header="新增组" />
                            <MenuItem Command="{Binding DeleteScriptGroupCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="删除组" />
                            <MenuItem Command="{Binding RenameScriptGroupCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="重命名" />
                            <MenuItem Command="{Binding CopyScriptGroupCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="复制组" />
                            <MenuItem Command="{Binding AddScriptGroupNextFlagCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="连续任务从此开始执行" />
                        </ContextMenu>
                    </ui:ListView.ContextMenu>
                </ui:ListView>
                <Grid Grid.Row="2">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <ui:Button Grid.Row="0"
                               Margin="2"
                               HorizontalAlignment="Stretch"
                               Command="{Binding StartMultiScriptGroupCommand}"
                               Content="连续执行" />

                    <ui:Button Grid.Row="1"
                               Margin="2"
                               HorizontalAlignment="Stretch"
                               Command="{Binding ContinueMultiScriptGroupCommand}"
                               Content="继续执行" />
                </Grid>
            </Grid>
        </Grid>

        <!--  分割  -->
        <ui:FontIcon Grid.Column="1"
                     HorizontalAlignment="Center" Glyph="&#xf7a5;" Style="{StaticResource FaFontIconStyle}" />
        <GridSplitter Grid.Column="1"
                      Width="12"
                      HorizontalAlignment="Stretch"
                      Background="{DynamicResource ControlStrongFillColorDisabledBrush}"
                      Focusable="False"
                      Opacity="0.4" />

        <!--  右侧栏:无选中组  -->
        <Border Grid.Column="2"
                Margin="6"
                Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                BorderThickness="1,1,1,1"
                CornerRadius="8">
            <Border.Style>
                <Style TargetType="Border">
                    <Setter Property="Visibility" Value="Visible" />
                    <Style.Triggers>
                        <DataTrigger
                            Binding="{Binding SelectedScriptGroup, Converter={StaticResource NotNullConverter}}"
                            Value="True">
                            <Setter Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            <Grid Margin="8,8,0,0">

                <StackPanel>
                    <ui:TextBlock Margin="0,0,0,8"
                                  FontTypography="BodyStrong"
                                  Text="（实验功能）请在左侧栏选择或新增配置组" />
                    <ui:TextBlock Margin="0,0,0,8"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        在左侧栏目右键可以新增/修改配置组，拖拽进行配置组排序。配置组内可以添加并配置软件内的 Javascript 脚本、键鼠脚本等，并能够控制执行次数、顺序等，
                        <Hyperlink Command="{Binding GoToScriptGroupUrlCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击查看调度器使用教程
                        </Hyperlink>
                    </ui:TextBlock>
                    <ui:TextBlock Margin="0,0,0,8"
                                  FontTypography="BodyStrong"
                                  Text="在左侧栏目右键可以新增配置组，或者直接点击下面按钮新增配置组" />
                    <ui:Button Margin="0,0,0,12"
                               Command="{Binding AddScriptGroupCommand}"
                               Content="新增配置组" />
                    <!--  示例配置组  -->
                    <!--<ui:TextBlock Margin="0,0,0,8"
                                  FontTypography="BodyStrong"
                                  Text="也可以直接点击导入下面的示例配置组" />
                    <ui:TextBlock Margin="0,0,0,8"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        <Hyperlink Command="{Binding ImportScriptGroupCommand}"
                                   CommandParameter="AutoCrystalflyExampleGroup"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            导入自动晶蝶示例配置组
                        </Hyperlink>
                    </ui:TextBlock>-->
                </StackPanel>
            </Grid>
        </Border>

        <!--  右侧栏:选中组  -->
        <Border Grid.Column="2"
                Margin="6"
                Background="{ui:ThemeResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{ui:ThemeResource CardStrokeColorDefaultBrush}"
                BorderThickness="1,1,1,1"
                CornerRadius="8">
            <Border.Style>
                <Style TargetType="Border">
                    <Setter Property="Visibility" Value="Visible" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding SelectedScriptGroup}" Value="{x:Null}">
                            <Setter Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            <Grid Margin="8,8,0,0">

                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal">
                    <ui:TextBlock Margin="0,0,0,8"
                                  FontTypography="BodyStrong"
                                  Text="（实验功能）配置组 - " />
                    <ui:TextBlock Margin="0,0,0,8"
                                  FontTypography="BodyStrong"
                                  Text="{Binding SelectedScriptGroup.Name}" />
                </StackPanel>

                <ui:TextBlock Grid.Row="1"
                              Margin="0,0,0,8"
                              Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                              TextWrapping="Wrap">
                    在下方列表中右键可以添加配置，拖拽可以调整执行顺序。支持 BetterGI 内的 Javascript 脚本、键鼠录制脚本等，通过调度器可以设置脚本执行次数、顺序等。
                </ui:TextBlock>


                <StackPanel Grid.Row="2" Orientation="Horizontal">
                    <ui:Button Command="{Binding StartScriptGroupCommand}"
                               Content="运行"
                               Icon="{ui:SymbolIcon Play24}" />
                    <Separator Width="10" Opacity="0" />


                    <ui:DropDownButton Content="添加" Icon="{ui:SymbolIcon Add24}">
                        <ui:DropDownButton.Flyout>
                            <ContextMenu> 
                                <MenuItem Header="JS脚本" Command="{Binding AddJsScriptCommand}" />
                                <MenuItem Header="地图追踪任务" Command="{Binding AddPathingCommand}" />
                                <MenuItem Header="键鼠脚本" Command="{Binding AddKmScriptCommand}" />
                                <MenuItem Header="Shell" Command="{Binding AddShellCommand}" />
                            </ContextMenu>
                        </ui:DropDownButton.Flyout>
                    </ui:DropDownButton>
                    <Separator Width="10" Opacity="0" />
                    <ui:DropDownButton Content="更多功能">
                        <ui:DropDownButton.Flyout>
                            <ContextMenu>
                                <MenuItem Header="清空" Command="{Binding ClearTasksCommand}" />
                                <MenuItem Header="日志分析" Command="{Binding OpenLogParseCommand}" />
                                <MenuItem Header="打开脚本仓库" Command="{Binding OpenLocalScriptRepoCommand}" />
                                <MenuItem Header="根据文件夹更新" Command="{Binding UpdateTasksCommand}" />
                                <MenuItem Header="任务倒序排列" Command="{Binding ReverseTaskOrderCommand}" />
                                <MenuItem Header="导出根据控制文件修改任务" Command="{Binding ExportMergerJsonsCommand}" />
                            </ContextMenu>
                        </ui:DropDownButton.Flyout>
                    </ui:DropDownButton>
                    <Separator Width="10" Opacity="0" />
                    <ui:Button Command="{Binding OpenScriptGroupSettingsCommand}"
                               Content="设置"
                               Icon="{ui:SymbolIcon Settings24}" />
                    <!--<ui:TextBlock VerticalAlignment="Center"
                                  FontSize="16"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        停止运行请配置并使用快捷键！
                    </ui:TextBlock>-->
                </StackPanel>

                <Separator Grid.Row="3"
                           Height="8"
                           Opacity="0" />

                <Grid Grid.Row="4">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="20" />
                    </Grid.ColumnDefinitions>
                    <Grid x:Name="Col1" Grid.Column="0" />
                    <Grid x:Name="Col2" Grid.Column="1" />
                    <Grid x:Name="Col3" Grid.Column="2" />
                    <Grid x:Name="Col4" Grid.Column="3" />
                </Grid>
                <ui:ListView Grid.Row="5"
                             HorizontalAlignment="Stretch"
                             VerticalAlignment="Stretch"
                             dd:DragDrop.IsDragSource="True"
                             dd:DragDrop.IsDropTarget="True"
                             dd:DragDrop.UseDefaultDragAdorner="True"
                             ItemsSource="{Binding SelectedScriptGroup.Projects, Mode=TwoWay}"
                             SelectionMode="Single">
     
                  
                    <ListView.View>

                        <GridView ColumnHeaderContainerStyle="{StaticResource GridViewColumnHeaderDarkStyle}">
                            <GridViewColumn Width="{Binding ElementName=Col1, Path=ActualWidth}"
                                            DisplayMemberBinding="{Binding Index}"
                                            Header="#" />
                            <GridViewColumn Width="{Binding ElementName=Col2, Path=ActualWidth}"
                                  
                                            Header="名称" >
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Name}" FontSize="14" ToolTip="{Binding FolderName}">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Foreground" Value="White" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding NextFlag}" Value="True">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>

                            </GridViewColumn>
                            <GridViewColumn Width="{Binding ElementName=Col3, Path=ActualWidth}"
                                            DisplayMemberBinding="{Binding TypeDesc}"
                                            Header="类型" />
                            <GridViewColumn Width="{Binding ElementName=Col4, Path=ActualWidth}" Header="启用状态">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <ui:ToggleSwitch
                                            IsChecked="{Binding Status, Converter={StaticResource BooleanToEnableTextConverter}, Mode=TwoWay}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                        </GridView>
                    </ListView.View>
                    <ListView.ContextMenu>
                        <ContextMenu>
                            <MenuItem Command="{Binding AddJsScriptCommand}" Header="添加JS脚本" />
                            <MenuItem Command="{Binding AddPathingCommand}" Header="添加地图追踪任务" />
                            <MenuItem Command="{Binding AddKmScriptCommand}" Header="添加键鼠脚本" />
                            <MenuItem Command="{Binding AddShellCommand}" Header="添加Shell" />
                            <MenuItem Command="{Binding AddNextFlagCommand}" Header="下一次任务从此处执行"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      />
                            
                            <MenuItem Command="{Binding EditScriptCommonCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="修改通用配置" />
                            <MenuItem Command="{Binding EditJsScriptSettingsCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="修改JS脚本自定义配置">
                                <MenuItem.Style>
                                    <Style TargetType="MenuItem">
                                        <Setter Property="Visibility" Value="Collapsed" />
                                        <Style.Triggers>
                                            <DataTrigger
                                                Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem.Type}"
                                                Value="Javascript">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </MenuItem.Style>
                            </MenuItem>
                            <MenuItem Command="{Binding DeleteScriptCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="移除" />
                            <MenuItem Command="{Binding DeleteScriptByFolderCommand}"
                                      CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                      Header="根据文件夹移除">
                                <MenuItem.Style>
                                    <Style TargetType="MenuItem">
                                        <Setter Property="Visibility" Value="Collapsed" />
                                        <Style.Triggers>
                                            <DataTrigger
                                                Binding="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem.Type}"
                                                Value="Pathing">
                                                <Setter Property="Visibility" Value="Visible" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </MenuItem.Style>
                            </MenuItem>
                        </ContextMenu>
                    </ListView.ContextMenu>
                    <ListView.Style>
                        <Style TargetType="{x:Type ListView}">
                            <Setter Property="BorderThickness" Value="0" />
                            <Setter Property="Background" Value="Transparent" />
                        </Style>
                    </ListView.Style>
                </ui:ListView>
            </Grid>
        </Border>
    </Grid>
</UserControl>
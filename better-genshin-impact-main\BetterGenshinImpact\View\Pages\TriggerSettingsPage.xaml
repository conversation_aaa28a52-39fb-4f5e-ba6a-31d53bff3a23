﻿<Page x:Class="BetterGenshinImpact.View.Pages.TriggerSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
      xmlns:system="clr-namespace:System;assembly=System.Runtime"
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      Title="HomePage"
      d:DataContext="{d:DesignInstance Type=pages:TriggerSettingsPageViewModel}"
      d:DesignHeight="950"
      d:DesignWidth="800"
      ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
      ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      FontFamily="{StaticResource TextThemeFontFamily}"
      Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      mc:Ignorable="d">

    <StackPanel Margin="42,16,42,12">

        <ui:TextBlock Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="实时触发的自动化任务设置" />
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon HandWave24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动拾取"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="选项不是NPC对话且不在黑名单时，自动按下 F 拾取/交互"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.AutoPickConfig.Enabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="极速拾取模式" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="打开时，将不再识别具体拾取内容，黑、白名单会失效" />
                    <ui:ToggleSwitch
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        Margin="0,0,36,0"
                        IsChecked="{Binding Config.AutoPickConfig.FastModeEnabled, Mode=TwoWay}" />
                </Grid>-->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择自动拾取文字识别引擎"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="Paddle可识别所有文字,速度慢,消耗少;Yap可识别部分文字,快且准,消耗大"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="100"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding PickOcrEngineNames}"
                              SelectedItem="{Binding Config.AutoPickConfig.OcrEngine, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="黑名单"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="排除 NPC 对话、各类交互选项、不需要拾取的物品等"
                                  TextWrapping="Wrap" />
                    <StackPanel Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                Orientation="Horizontal"
                                Margin="0,0,36,0">
                        <ui:ToggleSwitch
                            Margin="0,0,12,0"
                            IsChecked="{Binding Config.AutoPickConfig.BlackListEnabled, Mode=TwoWay}" />
                        <ui:Button
                            Command="{Binding EditBlacklistCommand}"
                            Content="前往设置" />
                    </StackPanel>

                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="白名单"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="需要主动按下 F 交互的内容，请配合黑名单使用"
                                  TextWrapping="Wrap" />
                    <StackPanel Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                Orientation="Horizontal"
                                Margin="0,0,36,0">
                        <ui:ToggleSwitch
                            Margin="0,0,12,0"
                            IsChecked="{Binding Config.AutoPickConfig.WhiteListEnabled, Mode=TwoWay}" />
                        <ui:Button
                            Command="{Binding EditWhitelistCommand}"
                            Content="前往设置" />
                    </StackPanel>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自定义拾取按键"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="默认为 F，自带了 E 按键，需要改成其他键的请阅读文档"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="80"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding PickButtonNames}"
                              SelectedItem="{Binding Config.AutoPickConfig.PickKey, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  自动剧情  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf075;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动剧情"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="快速跳过剧情文本、自动选择选项、自动提交物品等"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.Enabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用后台模式（会只用交互键进行剧情）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="游戏请不要最小化，不在对话中的时候鼠标会自动吸附回游戏中"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.RunBackgroundEnabled, Mode=TwoWay}" />
                </Grid>
                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择选项的方式"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="后台模式下会自动切换到使用交互键"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding SelectChatOptionTypeNames}"
                              SelectedItem="{Binding Config.AutoSkipConfig.SelectChatOptionType, Mode=TwoWay}" />
                </Grid>-->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="快速跳过对话文本"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="如果你要听完整的主线语音的话，可以关闭此功能"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.QuicklySkipConversationsEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择选项优先级"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="优先选择第一个选项、选择最后一个选项、不选择选项"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding ClickChatOptionNames}"
                              SelectedItem="{Binding Config.AutoSkipConfig.ClickChatOption, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择对话选项前的延迟（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="方便在自动剧情的情况下，依旧能够看清楚选项"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,24,0"
                                Text="{Binding Config.AutoSkipConfig.AfterChooseOptionSleepDelay, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动提交物品"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="对话过程中出现提交物品界面，会自动提交，支持多个物品"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.SubmitGoodsEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动关闭弹出页"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="关闭对话过程中出现的弹出页面，可能会误关地图、相机"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.ClosePopupPagedEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="凯瑟琳 - 自动领取『每日委托』奖励"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="在与凯瑟琳对话时，会自动“领取『每日委托』奖励"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.AutoGetDailyRewardsEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="凯瑟琳 - 自动重新派遣"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="自动领取已完成探索的奖励，并重新派遣"
                                  TextWrapping="Wrap" />
                    <!--<ui:Button
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        Content="配置"
                        Margin="0,0,16,0"
                        Command="{Binding OpenReExploreCharacterBoxCommand}" />-->
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="2"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.AutoReExploreEnabled, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>


        <!--  自动邀约  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xe5c8;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动邀约"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="自动剧情开启的情况下此功能才会生效，自动选择邀约选项"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.AutoHangoutEventEnabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="存在跳过按钮时自动点击"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="邀约过程中左上角存在跳过按钮时候，自动点击跳过"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.AutoSkipConfig.AutoHangoutPressSkipEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择邀约分支（不支持气泡联想选择）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="会按照选择分支的关键词进行选项选择"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding HangoutBranches, Mode=OneWay}"
                              SelectedItem="{Binding Config.AutoSkipConfig.AutoHangoutEndChoose, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="选择邀约选项前的延迟（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="方便在自动邀约的情况下，依旧能够看清楚选项"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,24,0"
                                Text="{Binding Config.AutoSkipConfig.AutoHangoutChooseOptionSleepDelay, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon FoodFish24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="半自动钓鱼"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="半自动钓鱼需要手动抛竿（全自动钓鱼已迁移至独立任务下）"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.AutoFishingConfig.Enabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="全自动钓鱼已迁移至独立任务下"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        全自动钓鱼已迁移至独立任务下，请到独立任务页配合快捷键使用
                    </ui:TextBlock>
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--  快速传送  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf3c5;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="快速传送"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="在大地图上点击传送点（或列表中有传送点）时，自动点击传送"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.QuickTeleportConfig.Enabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="点击候选列表传送点的间隔时间（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="普通用户请不要修改此配置，需要根据文字识别耗时配置，太低会导致点击失败"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.QuickTeleportConfig.TeleportListClickDelay, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="等待右侧传送弹出界面的时间（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="普通用户请不要修改此配置，不建议低于80ms，太低会导致传送按钮识别不到"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.QuickTeleportConfig.WaitTeleportPanelDelay, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用快捷键传送"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        按下
                        <Hyperlink Command="{Binding GoToHotKeyPageCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            [手动触发快速传送触发快捷键]
                        </Hyperlink>
                        后才进行快速传送
                    </ui:TextBlock>
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.QuickTeleportConfig.HotkeyTpEnabled, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>


        <!--  自动烹饪  -->
        <ui:CardControl Margin="0,0,0,12">
            <ui:CardControl.Icon>
                <ui:FontIcon Glyph="&#xe43f;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardControl.Icon>
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="自动烹饪"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        手动烹饪时，自动在完美状态下结束烹饪（不用的时候请关闭）
                    </ui:TextBlock>
                </Grid>
            </ui:CardControl.Header>
            <ui:ToggleSwitch Margin="0,0,40,0" IsChecked="{Binding Config.AutoCookConfig.Enabled, Mode=TwoWay}" />
        </ui:CardControl>

        <!--  一键战斗  -->
        <!--<ui:CardExpander Margin="0,0,0,12" ContentPadding="0" Icon="{ui:SymbolIcon ArrowSync24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="快捷战斗" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="实时识别当前出战角色，长按快捷后，循环执行当前角色战斗宏" />
                    <ui:ToggleSwitch
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        Margin="0,0,24,0"
                        IsChecked="{Binding Config.QuickTeleportConfig.Enabled, Mode=TwoWay}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="快捷键配置" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="普通用户请不要修改此配置，需要根据文字识别耗时配置，太低会导致点击失败" />
                    <ui:TextBox
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        MinWidth="90"
                        Margin="0,0,36,0"
                        Text="{Binding Config.QuickTeleportConfig.TeleportListClickDelay, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="等待右侧传送弹出界面的时间（毫秒）" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="普通用户请不要修改此配置，不建议低于80ms，太低会导致传送按钮识别不到" />
                    <ui:TextBox
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        MinWidth="90"
                        Margin="0,0,36,0"
                        Text="{Binding Config.QuickTeleportConfig.WaitTeleportPanelDelay, Mode=TwoWay}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>-->
    </StackPanel>
</Page>
﻿<UserControl x:Class="BetterGenshinImpact.View.Pages.KeyMouseRecordPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:BetterGenshinImpact.View.Pages"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             d:DataContext="{d:DesignInstance Type=pages:KeyMouseRecordPageViewModel}"
             d:DesignHeight="600"
             d:DesignWidth="800"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             FontFamily="{StaticResource TextThemeFontFamily}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/View/Controls/Style/ListViewEx.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Margin="42,16,42,12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <ui:TextBlock Grid.Row="0"
                      Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="键鼠录制回放功能（实验功能）" />
        <ui:TextBlock Grid.Row="1"
                      Margin="0,0,0,8"
                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                      TextWrapping="Wrap">
            建议在游戏内使用快捷键进行录制，录制完成后在调度器中使用。<Hyperlink Command="{Binding GoToKmScriptUrlCommand}" Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                点击查看键鼠录制回放功能教程
            </Hyperlink>
        </ui:TextBlock>

        <StackPanel Grid.Row="2" Orientation="Horizontal">
            <ui:Button Command="{Binding OpenScriptFolderCommand}"
                       Content="打开脚本目录"
                       Icon="{ui:SymbolIcon FolderOpen24}" />
            <Separator Width="10" Opacity="0" />
            <ui:Button Command="{Binding OpenLocalScriptRepoCommand}" Icon="{ui:SymbolIcon Archive24}">
                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                    <ui:TextBlock>脚本仓库</ui:TextBlock>
                    <ui:InfoBadge Margin="0,-8,-14,0"
                                  HorizontalAlignment="Right"
                                  VerticalAlignment="Top"
                                  Severity="Attention"
                                  Style="{DynamicResource DotInfoBadgeStyle}"
                                  Visibility="{Binding Config.ScriptConfig.ScriptRepoHintDotVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                </Grid>
            </ui:Button>
            <Separator Width="10" Opacity="0" />
            <ui:Button Command="{Binding StartRecordCommand}"
                       Content="开始录制"
                       Icon="{ui:SymbolIcon Record20}"
                       IsEnabled="{Binding IsRecording, Converter={StaticResource InverseBooleanConverter}}" />
            <Separator Width="10" Opacity="0" />
            <ui:Button Command="{Binding StopRecordCommand}"
                       Content="停止录制"
                       Icon="{ui:SymbolIcon Stop24}"
                       IsEnabled="{Binding IsRecording}" />
        </StackPanel>

        <Separator Grid.Row="3"
                   Height="10"
                   Opacity="0" />

        <Grid Grid.Row="4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="180" />
                <ColumnDefinition Width="120" />
                <ColumnDefinition Width="20" />
            </Grid.ColumnDefinitions>
            <Grid x:Name="Col1" Grid.Column="0" />
            <Grid x:Name="Col2" Grid.Column="1" />
            <Grid x:Name="Col3" Grid.Column="2" />
        </Grid>
        <ui:ListView Grid.Row="5"
                     HorizontalAlignment="Stretch"
                     VerticalAlignment="Stretch"
                     ItemsSource="{Binding ScriptItems}"
                     SelectionMode="Single">
            <ListView.View>
                <GridView ColumnHeaderContainerStyle="{StaticResource GridViewColumnHeaderDarkStyle}">
                    <GridViewColumn Width="{Binding ElementName=Col1, Path=ActualWidth}"
                                    DisplayMemberBinding="{Binding Name}"
                                    Header="名称" />
                    <GridViewColumn Width="{Binding ElementName=Col2, Path=ActualWidth}"
                                    DisplayMemberBinding="{Binding CreateTimeStr}"
                                    Header="创建时间" />
                    <GridViewColumn Width="{Binding ElementName=Col3, Path=ActualWidth}" Header="操作">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:Button Command="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:KeyMouseRecordPage}}, Path=DataContext.StartPlayCommand}"
                                               CommandParameter="{Binding Path}"
                                               Content="播放脚本"
                                               Icon="{ui:SymbolIcon Play24}" />
                                </StackPanel>

                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
            <ListBox.ContextMenu>
                <ContextMenu>
                    <MenuItem Command="{Binding EditScriptCommand}"
                              CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                              Header="修改名称" />
                    <MenuItem Command="{Binding DeleteScriptCommand}"
                              CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                              Header="删除" />
                </ContextMenu>
            </ListBox.ContextMenu>
            <ListView.Style>
                <Style TargetType="{x:Type ListView}">
                    <Setter Property="BorderThickness" Value="0" />
                    <Setter Property="Background" Value="Transparent" />
                </Style>
            </ListView.Style>
        </ui:ListView>
    </Grid>
</UserControl>
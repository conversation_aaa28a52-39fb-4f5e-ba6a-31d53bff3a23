<Page x:Class="BetterGenshinImpact.View.Pages.CommonSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      xmlns:emoji="clr-namespace:Emoji.Wpf;assembly=Emoji.Wpf"
      Title="CommonSettingsPage"
      d:DataContext="{d:DesignInstance Type=pages:CommonSettingsPageViewModel}"
      d:DesignHeight="2000"
      d:DesignWidth="600"
      ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
      ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      FontFamily="{StaticResource TextThemeFontFamily}"
      Foreground="{DynamicResource TextFillColorPrimaryBrush}"
      mc:Ignorable="d">

    <StackPanel Margin="42,16,42,12">
        <ui:TextBlock Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="软件设置" />

        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon LocalLanguage24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="游戏语言"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="Game Language"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="200"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding LanguageDict}"
                              SelectedValuePath="Key"
                              DisplayMemberPath="Value"
                              SelectedItem="{Binding Config.OtherConfig.GameCultureInfoName, Converter={StaticResource CultureInfoNameToKVPConverter}, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="SelectionChanged">
                                <b:InvokeCommandAction Command="{Binding GameLangSelectionChangedCommand}"
                                                       CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ComboBox>
                </Grid>

            </ui:CardControl.Header>
        </ui:CardControl>


        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon SquareHintSparkles24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用遮罩窗口"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="重启后生效"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.MaskEnabled, Mode=TwoWay}" />
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="Unchecked">
                            <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                        </b:EventTrigger>
                        <b:EventTrigger EventName="Checked">
                            <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="显示日志窗口"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        在遮罩内显示日志窗口，
                        <Hyperlink Command="{Binding GoToLogFolderCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            点击打开日志文件夹
                        </Hyperlink>
                    </ui:TextBlock>
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.ShowLogBox, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="Unchecked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                            <b:EventTrigger EventName="Checked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:ToggleSwitch>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="显示实时任务启用状态"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="在遮罩内显示实时任务启用状态"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.ShowStatus, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="Unchecked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                            <b:EventTrigger EventName="Checked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:ToggleSwitch>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="显示游戏FPS（实验功能）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="修改设置后重启生效，可能存在使游戏崩溃的BUG"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.ShowFps, Mode=TwoWay}" />
                </Grid>
                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="显示遮罩边框" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="围绕游戏窗口紫色的边框线" />
                    <ui:ToggleSwitch
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        Margin="0,0,36,0"
                        IsChecked="{Binding Config.MaskWindowConfig.ShowMaskBorder, Mode=TwoWay}" />
                </Grid>-->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="显示图像识别结果"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="实时显示各种图像识别的结果"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.DisplayRecognitionResultsOnMask, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用UID遮盖"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="遮盖右下角的UID区域"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.UidCoverEnabled, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="Unchecked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                            <b:EventTrigger EventName="Checked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:ToggleSwitch>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="显示小地图方位"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="在小地图周围显示东南西北的文字"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.DirectionsEnabled, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="Unchecked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                            <b:EventTrigger EventName="Checked">
                                <b:InvokeCommandAction Command="{Binding RefreshMaskSettingsCommand}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:ToggleSwitch>
                </Grid>


                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="遮罩以原神子窗体方式启动（实验功能）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="遮罩能够完全跟随窗体移动，但存在一些BUG，此配置重启后生效"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.MaskWindowConfig.UseSubform, Mode=TwoWay}" />
                </Grid>-->
            </StackPanel>
        </ui:CardExpander>

        <!--  截图  -->
        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon Camera24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用保存截图功能（开发者）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        可以通过快捷键保存截图，文件保存在
                        <Hyperlink Command="{Binding GoToFolderCommand}"
                                   Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                            log/screenshot
                        </Hyperlink>
                    </ui:TextBlock>
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.CommonConfig.ScreenshotEnabled, Mode=TwoWay}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="Unchecked">
                                <b:InvokeCommandAction Command="{Binding SwitchTakenScreenshotEnabledCommand}" />
                            </b:EventTrigger>
                            <b:EventTrigger EventName="Checked">
                                <b:InvokeCommandAction Command="{Binding SwitchTakenScreenshotEnabledCommand}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:ToggleSwitch>
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="截图快捷键"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="截图功能主要用于错误排查，训练素材快速获取等开发相关功能"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding GoToHotKeyPageCommand}"
                               Content="绑定快捷键" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="截图遮盖UID"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="生成的截图会遮盖右下角的UID区域"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.CommonConfig.ScreenshotUidCoverEnabled, Mode=TwoWay}" />
                </Grid>

            </StackPanel>
        </ui:CardExpander>

        <!--  退出时最小化  -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon ArrowMinimize24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="退出时最小化到系统托盘"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="启用后点击右上角退出按钮会最小化到系统托盘继续运行，右键托盘图标退出"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardControl.Header>
            <ui:ToggleSwitch Margin="0,0,36,0" IsChecked="{Binding Config.CommonConfig.ExitToTray, Mode=TwoWay}" />
        </ui:CardControl>


        <ui:TextBlock Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="通用功能设置" />
        <!--  按键绑定  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0" Icon="{ui:SymbolIcon KeyboardMouse16}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="按键绑定设置"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="如果你有游戏内改键需求，请在此配置对应的改键"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,20,0"
                               Command="{Binding OpenKeyBindingsWindowCommand}"
                               Content="配置" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="启用全局按键映射"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="使按键绑定设置对外部脚本生效"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.KeyBindingsConfig.GlobalKeyMappingEnabled}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        
        <!-- 地图追踪设置 -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon DataTreemap24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="地图追踪优先使用的特征匹配方式"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="影响所有地图追踪功能，重启后生效"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               MinWidth="120"
                               ItemsSource="{Binding MapPathingTypes}"
                               SelectedItem="{Binding Config.PathingConditionConfig.MapPathingType}"
                               />
                </Grid>

            </ui:CardControl.Header>
        </ui:CardControl>

        <!--  大地图传送相关设置  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf3c5;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="大地图地图传送设置"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="用于地图追踪、自动秘境、一条龙等功能中传送功能的配置"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="地图移动过程中是否缩放地图"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="建议开启，关闭该设置可能在运行部分脚本时发生错误"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.TpConfig.MapZoomEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="单次鼠标移动的最大距离"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="【100-2000】 过大可能会导致鼠标移动出窗口"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.TpConfig.MaxMouseMove, Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="地图缩小的距离"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="【>600】大于这个距离会缩小地图以加快传送"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.TpConfig.MapZoomOutDistance, Mode=TwoWay,                                 
                                ValidatesOnNotifyDataErrors=True}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="地图放大的距离"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="【200-600】小于这个距离会放大地图以提高移动的精度"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.TpConfig.MapZoomInDistance, Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="鼠标移动的时间间隔（毫秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="【2-100】数字越小移动鼠标的速度越快，如果移动地图时产生卡顿，请提高这个数值"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.TpConfig.StepIntervalMilliseconds,
                                Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>

        <!--回血相关设置-->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf0fa;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0" Grid.Column="0"
                                  Text="七天神像设置"
                                  FontTypography="Body" />
                    <ui:TextBlock Grid.Row="1" Grid.Column="0"
                                  Text="用于指定回血的七天神像"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0" Grid.Column="0"
                                  Text="是否就近七天神像恢复血量"
                                  FontTypography="Body" />
                    <ui:TextBlock Grid.Row="1" Grid.Column="0"
                                  Text="启用后将自动选择最近的七天神像，忽略下方指定位置"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}" />
                    <ui:ToggleSwitch Grid.Row="0" Grid.RowSpan="2" Grid.Column="1" Margin="0,0,36,0"
                                     IsChecked="{Binding Config.TpConfig.IsReviveInNearestStatueOfTheSeven}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0" Grid.Column="0"
                                  Text="传送到七天神像之后是否需要移动后回血"
                                  FontTypography="Body" />
                    <ui:TextBlock Grid.Row="1" Grid.Column="0"
                                  Text="启用后将自动向七天神像方向移动"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}" />
                    <ui:ToggleSwitch Grid.Row="0" Grid.RowSpan="2" Grid.Column="1" Margin="0,0,36,0"
                                     IsChecked="{Binding Config.TpConfig.ShouldMove}" />
                </Grid>-->
                <!-- 指定国家 -->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0" Grid.Column="0"
                                  Text="七天神像国家"
                                  FontTypography="Body" />
                    <ui:TextBlock Grid.Row="1" Grid.Column="0"
                                  Text="选择七天神像所在国家"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}" />
                    <ComboBox Grid.Row="0" Grid.RowSpan="2" Grid.Column="1"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding CountryList}"
                              SelectedItem="{Binding SelectedCountry}"
                              MinWidth="120" />
                </Grid>

                <!-- 指定区域 -->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0" Grid.Column="0"
                                  Text="七天神像区域"
                                  FontTypography="Body" />
                    <ui:TextBlock Grid.Row="1" Grid.Column="0"
                                  Text="选择七天神像所在区域"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}" />
                    <ComboBox Grid.Row="0" Grid.RowSpan="2" Grid.Column="1"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding Areas}"
                              SelectedItem="{Binding SelectedArea}"
                              MinWidth="120" />
                </Grid>

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="回血等待间隔（秒）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="【1.0-30.0】传送到七天神像之后需要等待多久恢复血量"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.TpConfig.HpRestoreDuration, Mode=TwoWay, 
                                UpdateSourceTrigger=PropertyChanged,
                                ValidatesOnNotifyDataErrors=True}" />
                </Grid>
            </StackPanel>
        </ui:CardExpander>
        <!-- 新增手动导入本地脚本仓库功能 -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Folder24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="手动导入本地脚本仓库（zip格式）"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="脚本仓库无法在线更新时使用"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding ImportLocalScriptsRepoZipCommand}"
                               Content="导入" />
                </Grid>

            </ui:CardControl.Header>
        </ui:CardControl>


        <!--  其他设置  -->
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0">
            <ui:CardExpander.Icon>
                <ui:FontIcon Glyph="&#xf141;" Style="{StaticResource FaFontIconStyle}" />
            </ui:CardExpander.Icon>
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="其他设置"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="设定一些其他功能的配置，失去焦点自动恢复等"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>

                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="BGI界面语言"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="BGI UI Language"
                                  TextWrapping="Wrap" />
                    <ComboBox Grid.Row="0"
                              Grid.RowSpan="2"
                              Grid.Column="1"
                              Width="200"
                              Margin="0,0,36,0"
                              ItemsSource="{Binding LanguageDict}"
                              SelectedValuePath="Key"
                              DisplayMemberPath="Value"
                              SelectedItem="{Binding Config.AutoFishingConfig.UiCultureInfoName, Converter={StaticResource CultureInfoNameToKVPConverter}, Mode=TwoWay}" />
                </Grid>-->
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="游戏失去焦点时候，强制恢复激活游戏窗口"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="适用于调度器任务和部分独立任务，失去焦点后，将自动激活游戏窗口，开启此项后，如果想切出游戏窗口，可以先暂停任务，之后再切出"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.OtherConfig.RestoreFocusOnLostEnabled, Mode=TwoWay}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="调度器路径追踪任务大地图传送过程中自动领取委托"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="调度器路径追踪任务中，打开大地图准备传送时，会首先检测是否存在需要领取的派遣任务，如果存在，则自动领取委托（调用一条龙领取奖励逻辑），选择领取城市后生效。"
                                  TextWrapping="Wrap" />
                    <ComboBox  MinWidth="100"
                               Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                              SelectedItem="{Binding Config.OtherConfig.AutoFetchDispatchAdventurersGuildCountry, Mode=TwoWay}"
                              ItemsSource="{Binding AdventurersGuildCountry}" />
                    <!--<ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.OtherConfig.RestoreFocusOnLostEnabled, Mode=TwoWay}" />-->
                </Grid>
                        <ui:CardExpander Margin="0,0,0,12"
                         ContentPadding="0"
                         Icon="{ui:SymbolIcon SquareHintSparkles24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="调度器异常重启配置"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="当调度器任务异常抛出未预期错误时，累计一定次数后，自动重启bgi，以恢复功能。"
                                  TextWrapping="Wrap" />
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,24,0"
                                     IsChecked="{Binding Config.OtherConfig.AutoRestartConfig.Enabled, Mode=TwoWay}" />
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="Unchecked">
                            <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                        </b:EventTrigger>
                        <b:EventTrigger EventName="Checked">
                            <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="异常次数"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="当运行调度器任务时，异常导致任务失败的计数，当达到计数时会重启bgi。"
                                  TextWrapping="Wrap" />
                    <ui:TextBox Grid.Row="0"
                                Grid.RowSpan="2"
                                Grid.Column="1"
                                MinWidth="90"
                                Margin="0,0,36,0"
                                Text="{Binding Config.OtherConfig.AutoRestartConfig.FailureCount, Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="是否同时重启游戏"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        重启bgi时，同时重启游戏，需开启首页启动配置：同时启动原神、自动进入游戏，此配置才会生效。
                    </ui:TextBlock>
                    
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.OtherConfig.AutoRestartConfig.RestartGameTogether, Mode=TwoWay}">
                    </ui:ToggleSwitch>
                    </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="战斗失败算异常"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                       在锄地脚本中，实际战斗成功次数大于等于预期战斗次数，才能成功，否则抛出异常。可以解决锄地时，应打开地图失败、切换队伍、或卡在按Z的切换界面、卡月卡等，可能导致的无限卡死。
                       但需要确定自己的队伍练度，有无可能连续超过配置失败次数个脚本都复活超次，导致判断失败，从而触发无限重启。
                    </ui:TextBlock>
                    
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.OtherConfig.AutoRestartConfig.IsFightFailureExceptional, Mode=TwoWay}">
                    </ui:ToggleSwitch>
                </Grid>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="路径未完全走完算异常"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap">
                        比战斗失败更严格的条件，勾选此项下，追踪文件，如果未执行完，算失败。此项勾选需要自身的追踪任务稳定性高。可以先关观察日志，打开异常情况统计下，有无连续标红超过失败次数的情况，有则不建议勾选。
                    </ui:TextBlock>
                    
                    <ui:ToggleSwitch Grid.Row="0"
                                     Grid.RowSpan="2"
                                     Grid.Column="1"
                                     Margin="0,0,36,0"
                                     IsChecked="{Binding Config.OtherConfig.AutoRestartConfig.IsPathingFailureExceptional, Mode=TwoWay}">
                    </ui:ToggleSwitch>
                </Grid>                
                
            </StackPanel>
        </ui:CardExpander>
            <!--锄地规划配置 -->
                <ui:CardExpander Margin="0,0,0,12"
                                 ContentPadding="0"
                                 Icon="{ui:SymbolIcon SquareHintSparkles24}">
                    <ui:CardExpander.Header>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="锄地规划配置"
                                          TextWrapping="Wrap" />
                            <ui:TextBlock Grid.Row="1"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="开启后，当每日（4点开始）统计锄地超过上限（需脚本支持，勾选允许锄地统计标志，或通过追踪文件目录的控制文件，来记录各追踪文件数据），会跳过接下来的锄地任务。"
                                          TextWrapping="Wrap" />
                            <ui:ToggleSwitch Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             IsChecked="{Binding Config.OtherConfig.FarmingPlanConfig.Enabled, Mode=TwoWay}" />
                            <b:Interaction.Triggers>
                                <b:EventTrigger EventName="Unchecked">
                                    <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                </b:EventTrigger>
                                <b:EventTrigger EventName="Checked">
                                    <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                </b:EventTrigger>
                            </b:Interaction.Triggers>
                        </Grid>
                    </ui:CardExpander.Header>
                    <StackPanel>
                        <Grid Margin="16">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="日精英上限"
                                          TextWrapping="Wrap" />
                            <ui:TextBlock Grid.Row="1"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="当锄地精英数量到达上限时，跳过主目标为精英的锄地脚本。"
                                          TextWrapping="Wrap" />
                            <ui:TextBox Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Grid.Column="1"
                                        MinWidth="90"
                                        Margin="0,0,36,0"
                                        Text="{Binding Config.OtherConfig.FarmingPlanConfig.DailyEliteCap, Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                        </Grid>
                        <Grid Margin="16">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="日小怪上限"
                                          TextWrapping="Wrap" />
                            <ui:TextBlock Grid.Row="1"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="当锄地小怪数量到达上限时，会跳过主目标为小怪的锄地脚本。"
                                          TextWrapping="Wrap" />
                            <ui:TextBox Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Grid.Column="1"
                                        MinWidth="90"
                                        Margin="0,0,36,0"
                                        Text="{Binding Config.OtherConfig.FarmingPlanConfig.DailyMobCap, Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                        </Grid>
                        <StackPanel>
                            <!-- 结合米游社数据计算上限 -->
                            <ui:CardExpander Margin="0,0,0,12"
                                             ContentPadding="0"
                                             Icon="{ui:SymbolIcon SquareHintSparkles24}">
                                <ui:CardExpander.Header>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <ui:TextBlock Grid.Row="0"
                                                      Grid.Column="0"
                                                      FontTypography="Body"
                                                      Text="结合米游社数据计算"
                                                      TextWrapping="Wrap" />
                                        <ui:TextBlock Grid.Row="1"
                                                      Grid.Column="0"
                                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                      Text="使用米游社数据（大概两三小时延时），对锄地数量进行修正，由于米游社cookie在一段时间后会失效，需要重新获取，所以如果当天存在米游社的数据，则上限需按照下面配置的来，否则按照上方配置的限制来（考虑到锄地之外的杀怪，如狗粮卡时间，所以估算值可能不一样）。"
                                                      TextWrapping="Wrap" />
                                        <ui:ToggleSwitch Grid.Row="0"
                                                         Grid.RowSpan="2"
                                                         Grid.Column="1"
                                                         Margin="0,0,24,0"
                                                         IsChecked="{Binding Config.OtherConfig.FarmingPlanConfig.MiyousheDataConfig.Enabled, Mode=TwoWay}" />
                                        <b:Interaction.Triggers>
                                            <b:EventTrigger EventName="Unchecked">
                                                <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                            </b:EventTrigger>
                                            <b:EventTrigger EventName="Checked">
                                                <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                            </b:EventTrigger>
                                        </b:Interaction.Triggers>
                                    </Grid>
                                </ui:CardExpander.Header>
                                <StackPanel>
                                    <Grid Margin="16">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <ui:TextBlock Grid.Row="0"
                                                      Grid.Column="0"
                                                      FontTypography="Body"
                                                      Text="日精英上限"
                                                      TextWrapping="Wrap" />
                                        <ui:TextBlock Grid.Row="1"
                                                      Grid.Column="0"
                                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                      Text="当锄地精英数量到达上限时，跳过主目标为精英的锄地脚本。"
                                                      TextWrapping="Wrap" />
                                        <ui:TextBox Grid.Row="0"
                                                    Grid.RowSpan="2"
                                                    Grid.Column="1"
                                                    MinWidth="90"
                                                    Margin="0,0,36,0"
                                                    Text="{Binding Config.OtherConfig.FarmingPlanConfig.MiyousheDataConfig.DailyEliteCap, Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                                    </Grid>
                                    <Grid Margin="16">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <ui:TextBlock Grid.Row="0"
                                                      Grid.Column="0"
                                                      FontTypography="Body"
                                                      Text="日小怪上限"
                                                      TextWrapping="Wrap" />
                                        <ui:TextBlock Grid.Row="1"
                                                      Grid.Column="0"
                                                      Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                                      Text="当锄地小怪数量到达上限时，会跳过主目标为小怪的锄地脚本。"
                                                      TextWrapping="Wrap" />
                                        <ui:TextBox Grid.Row="0"
                                                    Grid.RowSpan="2"
                                                    Grid.Column="1"
                                                    MinWidth="90"
                                                    Margin="0,0,36,0"
                                                    Text="{Binding Config.OtherConfig.FarmingPlanConfig.MiyousheDataConfig.DailyMobCap, Mode=TwoWay, 
                                ValidatesOnNotifyDataErrors=True}" />
                                    </Grid>
                                </StackPanel>
                            </ui:CardExpander>
                        </StackPanel>
                    </StackPanel>
                </ui:CardExpander>
                <!--米游社相关 -->
                                <ui:CardExpander Margin="0,0,0,12"
                                 ContentPadding="0"
                                 Icon="{ui:SymbolIcon SquareHintSparkles24}">
                    <ui:CardExpander.Header>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="米游社相关"
                                          TextWrapping="Wrap" />
                            <ui:TextBlock Grid.Row="1"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="用于配置米游社相关的信息如cookie"
                                          TextWrapping="Wrap" />

                            <b:Interaction.Triggers>
                                <b:EventTrigger EventName="Unchecked">
                                    <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                </b:EventTrigger>
                                <b:EventTrigger EventName="Checked">
                                    <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                </b:EventTrigger>
                            </b:Interaction.Triggers>
                        </Grid>
                    </ui:CardExpander.Header>
                    <StackPanel>
                        <Grid Margin="16">
                            <Grid.RowDefinitions>
                                <!-- 第一行高度自适应 -->
                                <RowDefinition Height="Auto" />
                                <!-- 第二行高度设为2*，使其占比更大 -->
                                <RowDefinition Height="2*" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <!-- 左侧文本列 -->
                                <ColumnDefinition Width="*" />
                                <!-- 右侧输入框列 -->
                                <ColumnDefinition Width="Auto" />
                                <!-- 新增按钮列 -->
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- 标题文本 -->
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="cookie"
                                          TextWrapping="Wrap"
                                          VerticalAlignment="Center"/>

                            <!-- 说明文本 - 占据两行 -->
                            <ui:TextBlock Grid.Row="0"
                                          Grid.RowSpan="2"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="米游社cookie"
                                          TextWrapping="Wrap"
                                          VerticalAlignment="Top"
                                          Margin="0,24,0,0"/> <!-- 增加上边距使文本向下偏移 -->

                            <!-- 输入框 - 占据两行 -->
                            <ui:TextBox Grid.Row="0"
                                        Grid.RowSpan="2"
                                        Grid.Column="1"
                                        MaxWidth="400" 
                                        MinWidth="400" 
                                        MinHeight="60" 
                                        Margin="0,0,8,0"
                                        VerticalAlignment="Stretch" 
                                        Text="{Binding Config.OtherConfig.MiyousheConfig.Cookie, Mode=TwoWay, 
                        ValidatesOnNotifyDataErrors=True}"/>

                            <!-- 新增帮助按钮 -->
                            <ui:Button Grid.Row="0"
                                       Grid.Column="2"
                                       Content="?"
                                       Width="50"
                                       Height="28"
                                       FontWeight="Bold"
                                       Margin="4,0,0,0"
                                       VerticalAlignment="Top"
                                       Command="{Binding QuestionButtonOnClickCommand}"
        
                            /> 
                            
                            <!-- 绑定点击事件 -->
                        </Grid>
                         <Grid  Margin="16">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <ui:TextBlock Grid.Row="0"
                                          Grid.Column="0"
                                          FontTypography="Body"
                                          Text="cookie与调度器日志处同步"
                                          TextWrapping="Wrap" />
                            <ui:TextBlock Grid.Row="1"
                                          Grid.Column="0"
                                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                          Text="开启后，当调度器日志更改，或此处更改，都会同步其cookie。"
                                          TextWrapping="Wrap" />
                            <ui:ToggleSwitch Grid.Row="0"
                                             Grid.RowSpan="2"
                                             Grid.Column="1"
                                             Margin="0,0,24,0"
                                             IsChecked="{Binding Config.OtherConfig.MiyousheConfig.LogSyncCookie, Mode=TwoWay}" />
                            <b:Interaction.Triggers>
                                <b:EventTrigger EventName="Unchecked">
                                    <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                </b:EventTrigger>
                                <b:EventTrigger EventName="Checked">
                                    <b:InvokeCommandAction Command="{Binding SwitchMaskEnabledCommand}" />
                                </b:EventTrigger>
                            </b:Interaction.Triggers>
                        </Grid>

                    </StackPanel>
                </ui:CardExpander>
            </StackPanel>
        </ui:CardExpander>
        <!--  地图  -->
        <!--<ui:CardControl Margin="0,0,0,12"
                        Icon="{ui:SymbolIcon Cursor24}"
                        Visibility="{markup:Converter Value={x:Static helpers:RuntimeHelper.IsDebuggerAttached},
                                                      Converter={StaticResource BooleanToVisibilityConverter}}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  TextWrapping="Wrap"
                                  Text="查看地图（开发者）" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  TextWrapping="Wrap"
                                  Text="查看当前识别到的位置" />
                </Grid>
            </ui:CardControl.Header>
            <ui:Button Margin="0,0,36,0"
                       Command="{Binding OpenMapViewerCommand}"
                       Content="查看地图" />
        </ui:CardControl>-->

        <!--<ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Keyboard24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock
                        Grid.Row="0"
                        Grid.Column="0"
                        FontTypography="Body"
                        TextWrapping="Wrap"
                        Text="aaaaaaa" />
                    <ui:TextBlock
                        Grid.Row="1"
                        Grid.Column="0"
                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                        TextWrapping="Wrap"
                        Text="xxxxxxxxxxxxxxxxxxxxxxxx" />
                </Grid>
            </ui:CardControl.Header>
            <ui:ToggleSwitch
                Margin="0,0,36,0"
                IsChecked="{Binding Config.MaskWindowConfig.ShowLogBox, Mode=TwoWay}"/>
        </ui:CardControl>-->

        <!-- About Option -->
        <ui:TextBlock Margin="0,0,0,8"
                      FontTypography="BodyStrong"
                      Text="帮助" />
                      
        <!-- Update Check Card -->
        
        <ui:CardExpander Margin="0,0,0,12" ContentPadding="0" Icon="{ui:SymbolIcon ArrowDownload24}">
            <ui:CardExpander.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="版本更新"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="检查软件是否有新版本可用"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,20,0"
                               Command="{Binding CheckUpdateCommand}"
                               Content="检查更新" />
                </Grid>
            </ui:CardExpander.Header>
            <StackPanel>
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <emoji:TextBlock Grid.Row="0"
                                     Grid.Column="0"
                                     Text="检查是否存在最新测试版"
                                     TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="【测试版】非常不稳定，请谨慎选择更新！"
                                  TextWrapping="Wrap" />

                    <StackPanel
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        Orientation="Horizontal">
                        <ui:Button 
                                   Margin="0,0,36,0"
                                   Command="{Binding CheckUpdateAlphaCommand}"
                                   Content="检查更新" />
                    </StackPanel>
                </Grid>
                <!--<Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <emoji:TextBlock Grid.Row="0"
                                     Grid.Column="0"
                                     Text="直接从 Github 获取最新测试版"
                                     TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="【测试版】非常不稳定，请谨慎选择更新！"
                                  TextWrapping="Wrap" />
                    <ui:Button Grid.Row="0"
                               Grid.RowSpan="2"
                               Grid.Column="1"
                               Margin="0,0,36,0"
                               Command="{Binding GotoGithubActionCommand}"
                               Content="访问 Github" />
                </Grid>-->
            </StackPanel>
        </ui:CardExpander>

        <!-- About Option -->
        <ui:CardControl Margin="0,0,0,12" Icon="{ui:SymbolIcon Info24}">
            <ui:CardControl.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ui:TextBlock Grid.Row="0"
                                  Grid.Column="0"
                                  FontTypography="Body"
                                  Text="关于 BetterGI"
                                  TextWrapping="Wrap" />
                    <ui:TextBlock Grid.Row="1"
                                  Grid.Column="0"
                                  Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                  Text="查看项目、文档等信息"
                                  TextWrapping="Wrap" />
                </Grid>
            </ui:CardControl.Header>
            <ui:Button Margin="0,0,36,0"
                       Command="{Binding OpenAboutWindowCommand}"
                       Content="查看" />
        </ui:CardControl>
    </StackPanel>
</Page>


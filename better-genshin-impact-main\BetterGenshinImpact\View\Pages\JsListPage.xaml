﻿<UserControl x:Class="BetterGenshinImpact.View.Pages.JsListPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:BetterGenshinImpact.View.Pages"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:pages="clr-namespace:BetterGenshinImpact.ViewModel.Pages"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:drawer="clr-namespace:BetterGenshinImpact.View.Controls.Drawer"
             d:DataContext="{d:DesignInstance Type=pages:JsListViewModel}"
             d:DesignHeight="600"
             d:DesignWidth="800"
             ui:Design.Background="{DynamicResource ApplicationBackgroundBrush}"
             ui:Design.Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             FontFamily="{StaticResource TextThemeFontFamily}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}"
             mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/View/Controls/Style/ListViewEx.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid Margin="42,16,42,12">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <ui:TextBlock Grid.Row="0"
                          Margin="0,0,0,8"
                          FontTypography="BodyStrong"
                          Text="自定义 Javascript 脚本（实验功能）" />
            <ui:TextBlock Grid.Row="1"
                          Margin="0,0,0,8"
                          Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                          TextWrapping="Wrap">
                可以通过 Javascript 调用 BetterGI 在原神中的各项能力。请在调度器中使用！
                <Hyperlink Command="{Binding GoToJsScriptUrlCommand}"
                           Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}">
                    点击查看 Javascript 脚本使用与编写教程
                </Hyperlink>
            </ui:TextBlock>

            <StackPanel Grid.Row="2" Orientation="Horizontal">
                <ui:Button Command="{Binding OpenScriptsFolderCommand}"
                           Content="打开脚本目录"
                           Icon="{ui:SymbolIcon FolderOpen24}" />
                <Separator Width="10" Opacity="0" />
                <ui:Button Command="{Binding OpenLocalScriptRepoCommand}" Icon="{ui:SymbolIcon Archive24}">
                    <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                        <ui:TextBlock>脚本仓库</ui:TextBlock>
                        <ui:InfoBadge Margin="0,-8,-14,0"
                                      HorizontalAlignment="Right"
                                      VerticalAlignment="Top"
                                      Severity="Attention"
                                      Style="{DynamicResource DotInfoBadgeStyle}"
                                      Visibility="{Binding Config.ScriptConfig.ScriptRepoHintDotVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    </Grid>
                </ui:Button>
            </StackPanel>

            <Separator Grid.Row="3"
                       Height="10"
                       Opacity="0" />

            <Grid Grid.Row="4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="80" />
                    <!-- <ColumnDefinition Width="120" /> -->
                    <ColumnDefinition Width="20" />
                </Grid.ColumnDefinitions>
                <Grid x:Name="Col1" Grid.Column="0" />
                <Grid x:Name="Col2" Grid.Column="1" />
                <Grid x:Name="Col3" Grid.Column="2" />
                <Grid x:Name="Col4" Grid.Column="3" />
                <!-- <Grid x:Name="Col5" Grid.Column="4" /> -->
            </Grid>
            <ui:ListView Grid.Row="5"
                         x:Name="ScriptsListView"
                         HorizontalAlignment="Stretch"
                         VerticalAlignment="Stretch"
                         ItemsSource="{Binding ScriptItems}"
                         SelectedItem="{Binding SelectedScriptProject, Mode=TwoWay}"
                         SelectionMode="Single">
                <b:Interaction.Triggers>
                    <b:EventTrigger EventName="PreviewMouseRightButtonDown">
                        <b:InvokeCommandAction Command="{Binding SetRightClickSelectionCommand}"
                                               CommandParameter="True" />
                    </b:EventTrigger>
                    <b:EventTrigger EventName="ContextMenuClosing">
                        <b:InvokeCommandAction Command="{Binding SetRightClickSelectionCommand}"
                                               CommandParameter="False" />
                    </b:EventTrigger>
                    <b:EventTrigger EventName="SelectionChanged">
                        <b:InvokeCommandAction Command="{Binding OpenScriptDetailDrawerCommand}"
                                               CommandParameter="{Binding ElementName=ScriptsListView, Path=SelectedItem}" />
                    </b:EventTrigger>
                </b:Interaction.Triggers>
                <ListView.View>
                    <GridView ColumnHeaderContainerStyle="{StaticResource GridViewColumnHeaderDarkStyle}">
                        <GridViewColumn Width="{Binding ElementName=Col1, Path=ActualWidth}"
                                        DisplayMemberBinding="{Binding FolderName}"
                                        Header="目录" />
                        <GridViewColumn Width="{Binding ElementName=Col2, Path=ActualWidth}"
                                        DisplayMemberBinding="{Binding Manifest.Name}"
                                        Header="名称" />
                        <GridViewColumn Width="{Binding ElementName=Col3, Path=ActualWidth}"
                                        DisplayMemberBinding="{Binding Manifest.Version}"
                                        Header="版本" />
                        <!--<GridViewColumn Width="{Binding ElementName=Col4, Path=ActualWidth}" Header="操作">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ui:Button Command="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type local:JsListPage}}, Path=DataContext.StartRunCommand}"
                                               CommandParameter="{Binding ProjectPath}"
                                               Content="执行脚本"
                                               Icon="{ui:SymbolIcon Play24}" />
                                </StackPanel>

                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>-->
                    </GridView>
                </ListView.View>
                <ListBox.ContextMenu>
                    <ContextMenu>
                        <MenuItem Command="{Binding OpenScriptProjectFolderCommand}"
                                  CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                  Header="打开目录" />
                        <MenuItem Command="{Binding StartRunCommand}"
                                  CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                  Header="执行脚本" />
                        <MenuItem Command="{Binding RefreshCommand}"
                                  CommandParameter="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                  Header="刷新" />
                    </ContextMenu>
                </ListBox.ContextMenu>
                <ListView.Style>
                    <Style TargetType="{x:Type ListView}">
                        <Setter Property="BorderThickness" Value="0" />
                        <Setter Property="Background" Value="Transparent" />
                    </Style>
                </ListView.Style>
                <ListView.ItemContainerStyle>
                    <Style BasedOn="{StaticResource ListViewItemStyle}" TargetType="ui:ListViewItem">
                        <Setter Property="ToolTip" Value="{Binding Manifest.Description}" />
                        <Setter Property="ToolTipService.InitialShowDelay" Value="0" />
                        <Setter Property="ToolTipService.ShowDuration" Value="60000" />
                    </Style>
                </ListView.ItemContainerStyle>
            </ui:ListView>
        </Grid>
        <!-- 抽屉控件 -->
        <drawer:CustomDrawer    
            IsOpen="{Binding DrawerVm.IsDrawerOpen, Mode=TwoWay}"
            DrawerPosition="{Binding DrawerVm.DrawerPosition}"
            OpenWidth="{Binding DrawerVm.DrawerWidth}"
            OpenHeight="{Binding DrawerVm.DrawerHeight}"
            DrawerBackground="{DynamicResource ControlFillColorDefaultBrush}"
            Content="{Binding DrawerVm.DrawerContent}"
            Panel.ZIndex="100">
            <b:Interaction.Triggers>
                <b:EventTrigger EventName="Opened">
                    <b:InvokeCommandAction Command="{Binding DrawerVm.OnDrawerOpenedCommand}"
                                           PassEventArgsToCommand="True"/>
                </b:EventTrigger>
                <b:EventTrigger EventName="Closing">
                    <b:InvokeCommandAction Command="{Binding DrawerVm.OnDrawerClosingCommand}"
                                           PassEventArgsToCommand="True"/>
                </b:EventTrigger>
            </b:Interaction.Triggers>
        </drawer:CustomDrawer>
    </Grid>
</UserControl>
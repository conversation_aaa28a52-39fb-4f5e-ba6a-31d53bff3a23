﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ui:ControlsDictionary />
    </ResourceDictionary.MergedDictionaries>
    <Style x:Key="GridViewColumnHeaderDarkStyle" TargetType="{x:Type GridViewColumnHeader}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type GridViewColumnHeader}">
                    <Grid Background="Transparent">
                        <Border MaxHeight="{TemplateBinding Height}"
                                Padding="{TemplateBinding Padding}"
                                BorderBrush="{DynamicResource ControlStrongFillColorDisabledBrush}"
                                BorderThickness="0,1,0,1">
                            <ContentPresenter Name="HeaderContent"
                                              Margin="5"
                                              Content="{TemplateBinding ContentControl.Content}"
                                              ContentStringFormat="{TemplateBinding ContentControl.ContentStringFormat}"
                                              ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}"
                                              RecognizesAccessKey="True"
                                              SnapsToDevicePixels="{TemplateBinding UIElement.SnapsToDevicePixels}" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="Foreground">
            <Setter.Value>
                <SolidColorBrush Color="{DynamicResource TextFillColorPrimary}" />
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>